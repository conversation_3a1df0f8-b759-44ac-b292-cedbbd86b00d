module.exports.region = "ap-south-1";
module.exports.cluster_identifier = "p-2621-aps1-01";
module.exports.subnets = () => {
    return ['subnet-03c0e75a996e5d89a', 'subnet-0a30b7344fdd68554']
};

module.exports.securityGroups = () => {
    return ['sg-00e275f6e979d7197']
};
module.exports.s3_bucket_name = "compliance-support";
module.exports.complianceRequirementsLayer = () => {
    return "arn:aws:lambda:ap-south-1:558533637587:layer:compliance-support-prod-dependencies:1";
};
