module.exports.region = "ap-southeast-1";
module.exports.cluster_identifier = "s-7587-apse1-11";
module.exports.s3_bucket_name = "compliance-support";
module.exports.complianceRequirementsLayer = () => {
    return "arn:aws:lambda:ap-southeast-1:558533637587:layer:compliance-support-staging-dependencies:8";
};
module.exports.subnets = () => {
    return ['subnet-06e0883942ee63d1c', 'subnet-0790fc3903c9bdfd9']
};
module.exports.securityGroups = () => {
    return ['sg-09c82d203a659d30b']
};
