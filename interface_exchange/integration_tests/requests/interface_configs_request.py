from interface_exchange.integration_tests.requests.base_request import BaseRequest
from interface_exchange.integration_tests.config.request_uris import *
from interface_exchange.integration_tests.config.common_config import SUCCESS_CODES


class InterfaceConfigsRequests(BaseRequest):

    def get_interface_configs_request(self, client, status_code):
        uri = get_interface_configs_uri
        response = self.request_processor(client, 'GET', uri, status_code)
            
        return response
