import os

import uvicorn


def runserver(reload=False) -> None:
    """Entry point of the application"""
    uvicorn.run(
        "hawkeye.app:create_app",
        workers=1,
        reload=reload,
        factory=True,
        host="0.0.0.0",
        port=int(os.environ.get('HAWKEYE_ADMIN_HOST_PORT', '8000')),
        forwarded_allow_ips='*',
        proxy_headers=True,
    )


if __name__ == "__main__":
    reload = True if os.environ.get('APP_ENV', 'development') == 'development' else False
    runserver(reload=reload)
