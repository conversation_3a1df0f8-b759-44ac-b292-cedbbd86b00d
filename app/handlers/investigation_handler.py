import base64
import json
import logging
from typing import Dict, Any

from requests_toolbelt import MultipartEncoder
from treebo_commons.request_tracing.context import setup_request_context_from_request_headers

from app.services.investigation_service import InvestigationService
from app.services.auth_service import AuthService
from app.utils.multipart_parser import parse_multipart_request

logger = logging.getLogger(__name__)


def handle_investigation_request(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler function specifically for handling investigation requests.

    Args:
        event: The Lambda event containing the investigation request
        context: The Lambda context

    Returns:
        Dict[str, Any]: The response containing investigation results or error
    """
    logger.info(f"Received investigation request {event.get('headers')} base64: {event.get('isBase64Encoded')}")
    setup_request_context_from_request_headers(event.get('headers', {}))
    try:
        # Validate authorization using AuthService
        is_valid, client_id = AuthService.validate_auth_header(event)
        if not is_valid:
            return {
                'statusCode': 401,
                'body': json.dumps({'error': 'Invalid Token'})
            }

        # Parse multipart/form-data request
        try:
            request_data = parse_multipart_request(event)
        except ValueError as e:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': str(e)})
            }

        # Process the investigation request using the service
        service = InvestigationService()
        investigation_response = service.process_investigation(request_data, client_id)

        # Convert the response to a dictionary for JSON serialization

        return {
            'statusCode': 200,
            'body': json.dumps(investigation_response)
        }

    except ValueError as e:
        logger.error(f"Validation error in investigation request: {str(e)}")
        return {
            'statusCode': 400,
            'body': json.dumps({'error': str(e)})
        }
    except Exception as e:
        logger.error(f"Error processing investigation request: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'Internal Server Error. Please try again later.'})
        }


if __name__ == '__main__':
    # Create a minimal valid PDF content for testing
    pdf_content = (b"%PDF-1.4\n%\xE2\xE3\xCF\xD3\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 "
                   b"obj\n<</Type/Pages/Kids[3 0 R]/Count 1>>\nendobj\n3 0 obj\n<</Type/Page/MediaBox[0 0 612 "
                   b"792]/Parent 2 0 R/Resources<<>>>>\nendobj\nxref\n0 4\n0000000000 65535 f\n0000000015 00000 "
                   b"n\n0000000060 00000 n\n0000000111 00000 n\ntrailer\n<</Size 4/Root 1 0 R>>\nstartxref\n180\n%%EOF")

    # Create a multipart form-data payload with PDF file
    fields = {
        "case_number": "FIR001",
        "phone": "+91 9660483910",
        "activity_duration": "60",  # Look back 60 days
        "notice": ("notice.pdf", pdf_content, "application/pdf")
    }
    multipart_encoder = MultipartEncoder(fields=fields)

    mock_event = {
        "headers": {
            "Content-Type": multipart_encoder.content_type,
            "Authorization": "Bearer SampleToken",
            "X-Tenant-Id": "treebo",
        },
        "body": base64.b64encode(multipart_encoder.to_string()).decode(),
        "isBase64Encoded": True,
        "httpMethod": "POST",
        "path": "/govt/law-enforcement-goa/fetch-user-information/"
    }

    # Mock context
    context = {}

    # Print the request details for debugging
    print(f"Content-Type: {multipart_encoder.content_type}")
    print(f"Body length: {len(mock_event['body'])}")

    # Process the request
    response = handle_investigation_request(mock_event, context)
    print(response)
