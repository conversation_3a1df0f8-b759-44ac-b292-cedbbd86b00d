import logging

import jwt
import os
from typing import Dict, Any, Tuple

logger = logging.getLogger(__name__)


class AuthService:
    @staticmethod
    def validate_auth_header(event: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validates the authorization header from the request.
        
        Args:
            event: The Lambda event containing headers
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        auth_header = event.get('headers', {}).get('Authorization', '')

        if not auth_header.startswith('Bearer '):
            return False, 'Invalid authorization header'

        token = auth_header.split(' ')[1]

        secret_key = os.environ.get('SECRET_KEY')
        if not secret_key:
            raise ValueError("SECRET_KEY environment variable is not set")
        clients = os.environ.get('CLIENTS', '').split(',')
        try:
            payload = jwt.decode(
                token,
                secret_key,
                algorithms=['HS256'],
                options={"verify_exp": False}
            )
            if payload.get('client_id') not in clients:
                logger.info(f"Invalid client: {payload.get('client_id')}")
                return False, ''
            return True, payload['client_id']
        except jwt.ExpiredSignatureError:
            logger.info("Token has expired")
            return False, ''
        except jwt.InvalidTokenError:
            logger.info("Invalid token")
            return False, ''
