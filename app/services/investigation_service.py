import asyncio
import logging
from typing import Dict, Any
import concurrent.futures
from datetime import datetime

from app.services.s3_service import S3Service
from app.services.user_profile_service import UserProfileService
from app.services.crs_service import CRSService
from app.utils.request_validator import InvestigationRequest
from app.utils.response_formatter import InvestigationResponse
from app.utils.phone_utils import extract_phone_number

logger = logging.getLogger(__name__)


class InvestigationService:
    def __init__(self):
        self.s3_service = S3Service()
        self.user_profile_service = UserProfileService()
        self.crs_service = CRSService()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)

    def process_investigation(self, request_data: Dict[str, Any], client_id: str = None) -> dict[str, Any]:
        """
        Process an investigation request by coordinating between different services.
        
        Args:
            request_data: The validated request data containing case details
        
        Returns:
            InvestigationResponse: Formatted investigation response
        
        Raises:
            ValueError: If request validation fails
            Exception: For other processing errors
        """
        # Validate request data
        validated_request = InvestigationRequest.from_dict(request_data)

        # Store notice in S3 if provided
        notice_url = None
        if validated_request.notice and validated_request.notice.get('filename'):
            notice_url = self.s3_service.store_notice(validated_request.notice, validated_request.case_number)
            logger.info(f"Notice stored at: {notice_url}")

        # Extract phone number without country code if present
        phone = None
        if validated_request.phone:
            phone = extract_phone_number(validated_request.phone)
            logger.info(f"Extracted phone number: {phone} from {validated_request.phone}")

        # Get user profile
        user_profiles = self.user_profile_service.get_profile(
            mobile_number=phone,
            email=validated_request.email_id
        )

        # Get user bookings with activity_duration
        bookings = self.crs_service.get_user_bookings(
            mobile_number=phone,
            email=validated_request.email_id,
            activity_duration=validated_request.activity_duration
        )
        investigation_response = InvestigationResponse(
            case_number=validated_request.case_number,
            phone=validated_request.phone,
            email_id=validated_request.email_id,
            name=user_profiles[0].name if user_profiles else None,
            contact_details=user_profiles,
            interactions=bookings
        )
        response = investigation_response.to_dict()
        # Asynchronously store investigation request details
        self._store_investigation_request_async(
            case_number=validated_request.case_number,
            phone=validated_request.phone,
            email=validated_request.email_id,
            notice_url=notice_url,
            client_id=client_id,
            response=response,
        )

        # Create and return the response using the InvestigationResponse model
        return response

    def _store_investigation_request_async(
        self, case_number: str, phone: str = None, email: str = None, notice_url: str = None,
        response: dict = None, client_id: str = None
    ) -> None:
        """
        Asynchronously store the investigation request details in UPS service.
        
        Args:
            case_number: The case number for the investigation
            phone: The phone number of the user
            email: The email of the user
            notice_url: The URL of the stored notice
        """
        try:
            # Create a new event loop in a separate thread
            loop = asyncio.new_event_loop()
            
            # Define a function to run in the new event loop
            def run_async_task():
                asyncio.set_event_loop(loop)
                try:
                    # Run the coroutine to completion in this thread's event loop
                    loop.run_until_complete(
                        self._store_investigation_details(
                            case_number=case_number,
                            client_id=client_id,
                            phone=phone,
                            email=email,
                            notice_url=notice_url,
                            response=response
                        )
                    )
                finally:
                    loop.close()
            
            # Submit the function to the executor to run in a separate thread
            self.executor.submit(run_async_task)
            
            logger.info(f"Async task to store investigation details for case {case_number} initiated")
        except Exception as e:
            # Log error but don't fail the main request
            logger.error(f"Failed to initiate async task to store investigation details: {str(e)}")

    async def _store_investigation_details(
        self, case_number: str, phone: str = None, email: str = None, notice_url: str = None, response=None,
        client_id: str = None
    ) -> None:
        """
        Store the investigation request details in UPS service.
        
        Args:
            case_number: The case number for the investigation
            phone: The phone number of the user
            email: The email of the user
            notice_url: The URL of the stored notice
        """
        try:
            # Create investigation details payload
            investigation_details = {
                "client_id": client_id,
                "case_number": case_number,
                "investigation_type": "law_enforcement",
                "timestamp": datetime.now().isoformat(),
                "notice_url": notice_url,
                "response": response
            }

            # Add the investigation details to the user profile
            await self.user_profile_service.add_investigation_record(
                mobile_number=phone,
                email=email,
                investigation_details=investigation_details
            )

            logger.info(f"Investigation details for case {case_number} stored successfully")
        except Exception as e:
            logger.error(f"Failed to store investigation details: {str(e)}")
