import os
from typing import Any, Dict

from app.constants import Network
from app.network.http_client import HttpClient
from app.utils.request_context import app_context

from .dispatcher import DispatcherBase


class BackendDispatcher(DispatcherBase):
    MESSAGE_TYPE_TO_BACKEND_CONFIG = {
        "GET_INTERFACES": {
            "method": "GET",
            "url": "/interface-exchange/v2/properties/{property_id}/interfaces",
        },
        "WAKEUP_REQUEST": {"method": "POST", "url": "/public/v1/wakeup-events"},
        "WAKEUP_CLEAR": {"method": "POST", "url": "/public/v1/wakeup-events"},
        "HOUSEKEEPING_STATUS": {
            "method": "POST",
            "url": "/public/v1/housekeeping-status",
        },
        "TELEPHONE_CHARGE": {"method": "POST", "url": "/public/v1/call-charges"},
        "MINIBAR_CHARGE": {"method": "POST", "url": "/public/v1/minibar-charges"},
    }

    def __init__(self):
        self.http_client = HttpClient()

    def get_headers(self):
        headers = {}
        headers["X-Tenant-Id"] = app_context.get_tenant_id()
        headers["X-Property-Id"] = app_context.get_property_id()
        return headers

    def get_dispatch_config(self, api: str) -> tuple:
        """
        Returns HTTP method and URL to be used based on message_type.
        """
        # TODO: Generate this environment variable using service registry at the time of deployment
        base_url = os.getenv("INTERFACE_EXCHANGE_BACKEND_URL")
        if api in self.MESSAGE_TYPE_TO_BACKEND_CONFIG:
            return (
                self.MESSAGE_TYPE_TO_BACKEND_CONFIG[api]["method"],
                f"{base_url}" + self.MESSAGE_TYPE_TO_BACKEND_CONFIG[api]["url"],
            )
        else:
            raise ValueError(f"Unsupported api for BackendDispatcher: {api}")

    async def call_interface_exchange_api(
        self,
        http_method: str,
        url: str,
        params: dict | None = None,
        payload: dict | None = None,
        headers: dict | None = None,
    ):
        """
        Call Interface Exchange Backend API.
        """
        request_func = getattr(self.http_client, http_method.lower())
        if http_method == "GET":
            response = await request_func(url, params=params, headers=headers)
        else:
            response = await request_func(url, json=payload, headers=headers)
        return response

    async def get_interfaces(self):
        headers = self.get_headers()
        params = {"network": Network.CLOUD.value}
        http_method, url = self.get_dispatch_config("GET_INTERFACES")
        url = url.format(property_id=app_context.get_property_id())
        response = await self.call_interface_exchange_api(
            http_method, url, params=params, headers=headers
        )
        interfaces = response["data"]["interfaces"]
        return interfaces
