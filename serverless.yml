service: compliance-support

#org: hotelsuperheroprivate
#app: compliance-support

frameworkVersion: '3.33.0'

provider:
  name: aws
  runtime: python3.11
  stage: ${opt:stage, 'staging'}
  region: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):region}
  memorySize: 128
  timeout: 120
  logRetentionInDays: 30
  deploymentPrefix: compliance-support-artifacts
  tags:
    project: compliance-support
    environment: ${opt:stage, self:provider.stage}
  environment:
    SENTRY_DSN:  ${file(serverless/config/${opt:stage, self:provider.stage}/secrets.yml):SENTRY_DSN}
    SERVICE_NAME: ${self:service}
    APP_ENV: ${opt:stage, self:provider.stage}
    CLUSTER_IDENTIFIER: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):cluster_identifier}
    APP_NAME: compliance-support
    AWS_SECRET_PREFIX: 'apps/compliance-support'
    S3_BUCKET_NAME: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):s3_bucket_name}
    THSC_ENVIRONMENT: ${opt:stage, self:provider.stage}
    SECRET_KEY: ${file(serverless/config/${opt:stage, self:provider.stage}/secrets.yml):SECRET_KEY}
    CLIENTS: ${file(serverless/config/${opt:stage, self:provider.stage}/secrets.yml):CLIENTS}
  vpc:
    securityGroupIds: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):securityGroups}
    subnetIds: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):subnets}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
          Resource: arn:aws:secretsmanager:${self:provider.region}:${aws:accountId}:secret:*
        - Effect: Allow
          Action:
            - s3:PutObject
            - s3:GetObject
          Resource: arn:aws:s3:::compliance-support
        - Effect: Allow
          Action:
            - ssm:GetParameter
            - ssm:GetParameters
            - ssm:GetParametersByPath
          Resource: arn:aws:ssm:${self:provider.region}:${aws:accountId}:parameter/*
  apiGateway:
    binaryMediaTypes:
      - 'multipart/form-data'

functions:
  handleInvestigationRequest:
    handler: app.handlers.investigation_handler.handle_investigation_request
    layers:
      - ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):complianceRequirementsLayer}
    events:
      - http:
          path: govt/compliance_support/fetch-user-information
          method: post
          cors: true
          contentHandling: CONVERT_TO_BINARY
    environment:
      LOG_LEVEL: "INFO"
    timeout: 30
    memorySize: 256

#custom:
#  logLevel: "INFO"
#  pythonRequirements:
#    dockerizePip: false
#    slim: true
#    useStaticCache: true
#    useDownloadCache: true
#    fileName: requirements-lambda.txt
#    layer:
#      name: ${self:service}-${self:provider.stage}-dependencies
#      description: Python dependencies for ${self:service}
#      compatibleRuntimes:
#        - python3.11

#plugins:
# - serverless-python-requirements

package:
  patterns:
    - '!**'
    - 'app/**'
    - '!app/**/__pycache__/**'
