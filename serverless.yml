service: compliance-support

#org: hotelsuperheroprivate
#app: compliance-support

frameworkVersion: '3.33.0'

provider:
  name: aws
  runtime: python3.11
  stage: ${opt:stage, 'staging'}
  region: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):region}
  memorySize: 256
  timeout: 30
  logRetentionInDays: 30
  deploymentPrefix: compliance-support-artifacts
  tags:
    project: compliance-support
    environment: ${opt:stage, self:provider.stage}
  environment:
    SENTRY_DSN:  ${file(serverless/config/${opt:stage, self:provider.stage}/secrets.yml):SENTRY_DSN}
    SERVICE_NAME: ${self:service}
    APP_ENV: ${opt:stage, self:provider.stage}
    CLUSTER_IDENTIFIER: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):cluster_identifier}
    APP_NAME: compliance-support
    AWS_SECRET_PREFIX: 'apps/compliance-support'
    S3_BUCKET_NAME: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):s3_bucket_name}
    THSC_ENVIRONMENT: ${opt:stage, self:provider.stage}
    SECRET_KEY: ${file(serverless/config/${opt:stage, self:provider.stage}/secrets.yml):SECRET_KEY}
    CLIENTS: ${file(serverless/config/${opt:stage, self:provider.stage}/secrets.yml):CLIENTS}
  vpc:
    securityGroupIds: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):securityGroups}
    subnetIds: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):subnets}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
          Resource: arn:aws:secretsmanager:${self:provider.region}:${aws:accountId}:secret:*
        - Effect: Allow
          Action:
            - s3:PutObject
            - s3:GetObject
          Resource: arn:aws:s3:::compliance-support
        - Effect: Allow
          Action:
            - ssm:GetParameter
            - ssm:GetParameters
            - ssm:GetParametersByPath
          Resource: arn:aws:ssm:${self:provider.region}:${aws:accountId}:parameter/*
  httpApi:
    throttle:
      rateLimit: 5
      burstLimit: 2000

package:
  patterns:
    - '!**'
    - 'app/**'
    - '!app/**/__pycache__/**'

functions:
  jokerHandler:
    handler: app.main_handler.handler
    layers:
      # - ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):jokerRequirementsLayer}
      - { Ref: PythonRequirementsLambdaLayer } # Uncomment to use auto-generated layer
    events:
      - sns: joker-events-topic
      - http:
          path: /api/v1/
          method: ANY

custom:
  pythonRequirements:
    dockerizePip: true
    usePoetry: false
    slim: true
    useStaticCache: false
    useDownloadCache: false
    layer: true  # Disabled auto layer creation - using pre-built layer instead
    # Set to true when you need to rebuild the layer after requirements change

  secretsFilePathPrefix: serverless
  secrets: ${file(serverless/secrets.${opt:stage, self:provider.stage}.yml)}

plugins:
  - serverless-python-requirements
  - serverless-secrets-plugin
