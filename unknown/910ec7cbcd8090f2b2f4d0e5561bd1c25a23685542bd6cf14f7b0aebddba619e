from rate_manager.infrastructure.models.rate_plans import RatePlanCompanyProfileModel
from rate_manager.domain.entities.aggregate_roots.rate_plan_company_profile import RatePlanCompanyProfile


class RatePlanCompanyProfileAdaptor:
    @staticmethod
    def to_domain_entity(db_model: RatePlanCompanyProfileModel) -> RatePlanCompanyProfile:
        return RatePlanCompanyProfile(
            rate_plan_id=db_model.rate_plan_id.hex,
            company_profile_id=db_model.company_profile_id
        )

    @staticmethod
    def to_db_entity(domain_entity: RatePlanCompanyProfile) -> RatePlanCompanyProfileModel:
        return RatePlanCompanyProfileModel(
            rate_plan_id=domain_entity.rate_plan_id,
            company_profile_id=domain_entity.company_profile_id
        )
