import logging

from rackrate_service.application.catalog_sync_service import CatalogSyncService
from rackrate_service.infrastructure.consumers.base_consumer import BaseConsumer
from rackrate_service.infrastructure.consumers.serializers.cs_catalog_schema import (
    CatalogChannelPayloadSchema,
)

logger = logging.getLogger("consumer." + __name__)


class CatalogChannelConsumer(BaseConsumer):
    """
    Documentation link: https://treebo.atlassian.net/wiki/spaces/EN/pages/********/Exchange+and+Message+Structure
    event: Property Message (routing_key = com.cs.treebo.channel)
    """

    def __init__(self):
        try:
            consumer_name = "cataloging_service_channel_sync"
            super(CatalogChannelConsumer, self).__init__(consumer_name)
        except Exception as ex:
            logger.exception(ex)

    def on_message(self, event_dict, message):
        try:
            channel_info_dto, error = CatalogChannelPayloadSchema().load(event_dict)
            if error:
                logger.error(
                    "CSChannelSyncConsumer data format error,Data:%s, Error:%s"
                    % (event_dict, error)
                )
                return
            CatalogSyncService().sync_channel_data(channel_info_dto)

        except Exception as ex:
            logger.exception(
                "Exception(%s) occurred in CSChannelSyncConsumer. details: %s"
                % (str(ex), event_dict)
            )
