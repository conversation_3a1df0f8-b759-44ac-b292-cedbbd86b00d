from collections import namedtuple


GuestFeedbackParams = namedtuple(
    'GuestFeedbackParams',
    [
        'hotel_id',
        'guest_email',
        'first_name',
        'last_name',
        'check_in',
        'check_out',
        'lang',
    ]
)

CSV_COLUMNS = GuestFeedbackParams(
    'HOTEL_ID',
    'PROP_GUEST_EMAIL',
    'PROP_FIRST_NAME',
    'PROP_LAST_NAME',
    'PROP_CHECK_IN',
    'PROP_CHECK_OUT',
    'PROP_LANG',
)

CSV_COLUMNS_ORDER = (
    CSV_COLUMNS.hotel_id,
    'column1',
    'column2',
    CSV_COLUMNS.first_name,
    CSV_COLUMNS.last_name,
    'column5',
    'column6',
    CSV_COLUMNS.check_in,
    CSV_COLUMNS.check_out,
    'column9',
    CSV_COLUMNS.guest_email,
    'column11',
    'column12',
    'column13',
    'column14',
    'column15',
    CSV_COLUMNS.lang,
    'column17',
    'column18',
    'column19',
    'column20',
    'column21',
    'column22',
    'column23',
)
