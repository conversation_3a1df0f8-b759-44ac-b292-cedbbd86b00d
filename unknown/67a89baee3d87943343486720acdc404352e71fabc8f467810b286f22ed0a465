from tenant_gateway.domain.entities.qms_guest_feedback import QMSGuestFeedbackEntity
from tenant_gateway.domain.models import QMSGuestFeedbackModel


class QMSGuestFeedbackAdaptor:
    @staticmethod
    def to_db_model(domain_entity: QMSGuestFeedbackEntity):
        return QMSGuestFeedbackModel(booking_ids=domain_entity.booking_ids,
                                     hotel_id=domain_entity.hotel_id,
                                     business_date=domain_entity.business_date,
                                     request_data=domain_entity.request_data,
                                     ack_status=domain_entity.ack_status,
                                     failure_reason=domain_entity.failure_reason)

    @staticmethod
    def to_domain_entity(db_model: QMSGuestFeedbackModel):
        return QMSGuestFeedbackEntity(booking_ids=db_model.booking_ids,
                                      hotel_id=db_model.hotel_id,
                                      business_date=db_model.business_date,
                                      request_data=db_model.request_data,
                                      ack_status=db_model.ack_status,
                                      failure_reason=db_model.failure_reason)
