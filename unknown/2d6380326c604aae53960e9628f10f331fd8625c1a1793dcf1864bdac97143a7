from sqlalchemy import Column, String
from sqlalchemy.dialects.postgresql import JSONB
from treebo_commons.multitenancy.sqlalchemy import db_engine
from rate_manager.infrastructure.common_models import TimeStampedMixin, DeleteMixin


class PackageModel(db_engine.Base, TimeStampedMixin, DeleteMixin):
    __tablename__ = "package"

    package_id = Column(String, primary_key=True)
    property_id = Column(String, nullable=False, index=True)
    package_name = Column(String, nullable=False)
    inclusions = Column(JSONB)
