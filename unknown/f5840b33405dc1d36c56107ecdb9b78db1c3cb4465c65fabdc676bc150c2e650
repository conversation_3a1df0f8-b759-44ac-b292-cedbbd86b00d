from dataclasses import dataclass
from decimal import Decimal

import datetime
from typing import Any, Set, Union

from core.common.utils.decimals import quantize_and_round_off


@dataclass(eq=True, unsafe_hash=True)
class DatePrice:
    uid: str
    date: datetime.date
    sell_price: Union[Decimal, None]
    list_price: Union[Decimal, None]

    def __post_init__(self):
        self.uid = str(self.uid)
        assert isinstance(self.date, datetime.date)
        if self.sell_price is not None:
            self.sell_price = quantize_and_round_off(self.sell_price)
        if self.list_price is not None:
            self.list_price = quantize_and_round_off(self.list_price)


@dataclass
class Price:
    property: Any
    sku: Any
    c_sc_p: Any

    date_prices: Set[DatePrice]

    def __post_init__(self):
        if not isinstance(self.date_prices, set):
            self.date_prices = set(self.date_prices)

    def __getitem__(self, date):
        assert isinstance(date, datetime.date)
        matched_prices = [dp for dp in self.date_prices if dp.date == date]
        try:
            return matched_prices[0]
        except IndexError:
            raise KeyError(f'Invalid date for Price: {date}')
