from sqlalchemy import Column, String, NUMERIC, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from treebo_commons.multitenancy.sqlalchemy import db_engine

from rate_manager.infrastructure.common_models import TimeStampedMixin


class InclusionRackRateModel(db_engine.Base, TimeStampedMixin):
    __tablename__ = "inclusion_rack_rate"

    id = Column(Integer, primary_key=True, autoincrement=True)
    property_id = Column(String, nullable=False)
    sku_id = Column(String, nullable=False)
    sku_category_code = Column(String, nullable=False)
    price = Column(NUMERIC(precision=15, scale=4), nullable=False)
    display_name = Column(String)
    description = Column(String, allow_none=True)
    extra_information = Column(JSONB, allow_none=True)

    __table_args__ = (
        UniqueConstraint("property_id", "sku_id"),
    )


class RoomRackRateModel(db_engine.Base, TimeStampedMixin):
    __tablename__ = "room_rack_rate"

    id = Column(Integer, primary_key=True, autoincrement=True)
    property_id = Column(String, nullable=False)
    room_type_id = Column(String, nullable=False)
    adult_count = Column(Integer, nullable=False)
    price = Column(NUMERIC(precision=15, scale=4), nullable=False)
    room_type_name = Column(String, nullable=False)

    __table_args__ = (
        UniqueConstraint("property_id", "room_type_id", "adult_count"),
    )
