import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock

import httpx
from app.network.http_client import <PERSON>ttp<PERSON><PERSON>, RETRYABLE_STATUS_CODES
from app.utils.request_context import app_context

@pytest.mark.asyncio
async def test_get_success():
    # Test successful GET request
    client = HttpClient()

    # Mock app_context
    with patch.object(app_context, 'get_request_id', return_value="test-request-id"):
        # Mock httpx.AsyncClient
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.json.return_value = {"data": "test"}
        mock_response.raise_for_status = MagicMock()

        with patch('httpx.AsyncClient.request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response

            # Call the method
            result = await client.get("https://example.com", params={"param": "value"})

            # Verify httpx.AsyncClient.request was called with correct parameters
            mock_request.assert_called_once()
            args, kwargs = mock_request.call_args

            # Check method and URL
            assert kwargs["method"] == "GET"
            assert kwargs["url"] == "https://example.com"

            # Check params
            assert kwargs["params"] == {"param": "value"}

            # Check headers
            assert kwargs["headers"]["X-Request-Id"] == "test-request-id"

            # Check result
            assert result == {"data": "test"}

@pytest.mark.asyncio
async def test_post_success():
    # Test successful POST request
    client = HttpClient()

    # Mock app_context
    with patch.object(app_context, 'get_request_id', return_value="test-request-id"):
        # Mock httpx.AsyncClient
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.json.return_value = {"data": "test"}
        mock_response.raise_for_status = MagicMock()

        with patch('httpx.AsyncClient.request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response

            # Call the method
            result = await client.post("https://example.com", json={"data": "test"})

            # Verify httpx.AsyncClient.request was called with correct parameters
            mock_request.assert_called_once()
            args, kwargs = mock_request.call_args

            # Check method and URL
            assert kwargs["method"] == "POST"
            assert kwargs["url"] == "https://example.com"

            # Check json
            assert kwargs["json"] == {"data": "test"}

            # Check headers
            assert kwargs["headers"]["X-Request-Id"] == "test-request-id"

            # Check result
            assert result == {"data": "test"}

@pytest.mark.asyncio
async def test_retry_on_retryable_status():
    # Test retry on retryable status code
    client = HttpClient(max_retries=2, backoff=[0.1, 0.1])

    # Mock app_context
    with patch.object(app_context, 'get_request_id', return_value="test-request-id"):
        # Mock httpx.AsyncClient
        mock_response_retry = MagicMock()
        mock_response_retry.status_code = 503  # Retryable status code

        # Create an HTTPStatusError that will be raised on the first attempt
        retry_error = httpx.HTTPStatusError(
            message="Service Unavailable",
            request=MagicMock(),
            response=mock_response_retry
        )

        mock_response_success = MagicMock()
        mock_response_success.status_code = 200
        mock_response_success.headers = {"content-type": "application/json"}
        mock_response_success.json.return_value = {"data": "test"}
        mock_response_success.raise_for_status = MagicMock()

        # Create a real sleep function that doesn't actually sleep
        async def fake_sleep(seconds):
            pass

        with patch('httpx.AsyncClient.request', new_callable=AsyncMock) as mock_request, \
             patch('asyncio.sleep', fake_sleep):
            # First call raises retryable error, second call succeeds
            mock_request.side_effect = [retry_error, mock_response_success]

            # Call the method
            result = await client.get("https://example.com")

            # Verify httpx.AsyncClient.request was called twice
            assert mock_request.call_count == 2

            # Check result
            assert result == {"data": "test"}
