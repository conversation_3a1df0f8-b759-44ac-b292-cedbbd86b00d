import pytest
from unittest.mock import patch, AsyncMock, MagicMock

from app.integrations.wifi.levotel import LevotelWifiIntegration
from app.exceptions import LevotelIntegrationException
from app.utils.request_context import app_context
from app.network.http_client import HttpClient

@pytest.mark.asyncio
async def test_handle_guest_checkin():
    # Test handling guest checkin message
    interface = {
        "configs": {
            "levotel_hotel_id": "test-api-key"
        }
    }

    interface_config = {
        "base_url": "https://test-vendor.com"
    }

    integration = LevotelWifiIntegration(interface, interface_config)

    # Create a mock HTTP client that doesn't actually make requests
    mock_http_client = AsyncMock()
    mock_http_client.get = AsyncMock(return_value={"status": "success"})

    # Mock the HttpClient class to avoid actual HTTP requests
    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request:
        mock_request.return_value = {"status": "success"}

        # Create test payload
        payload = {
            "room_number": "101",
            "guest_last_name": "Do<PERSON>",
            "guest_first_name": "<PERSON>"
        }

        # Call the method
        await integration.handle_guest_checkin(payload)

        # Verify HTTP client was called
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args

        # Check method
        assert args[0] == "GET"

        # Check URL
        assert args[1] == "https://test-vendor.com/api/"

        # Check params
        assert kwargs["params"] == {
            "op": "checkin",
            "apikey": "test-api-key",
            "RN": "101",
            "GN": "Doe"
        }

@pytest.mark.asyncio
async def test_handle_guest_checkout():
    # Test handling guest checkout message
    interface = {
        "configs": {
            "levotel_hotel_id": "test-api-key"
        }
    }

    interface_config = {
        "base_url": "https://test-vendor.com"
    }

    integration = LevotelWifiIntegration(interface, interface_config)

    # Mock the HttpClient class to avoid actual HTTP requests
    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request:
        mock_request.return_value = {"status": "success"}

        # Create test payload
        payload = {
            "room_number": "101",
            "guest_last_name": "Doe",
            "guest_first_name": "John"
        }

        # Call the method
        await integration.handle_guest_checkout(payload)

        # Verify HTTP client was called
        mock_request.assert_called_once()
        args, kwargs = mock_request.call_args

        # Check method
        assert args[0] == "GET"

        # Check URL
        assert args[1] == "https://test-vendor.com/api/"

        # Check params
        assert kwargs["params"] == {
            "op": "checkout",
            "apikey": "test-api-key",
            "RN": "101",
            "GN": "Doe"
        }

@pytest.mark.asyncio
async def test_handle_pms_message():
    # Test handling PMS message
    interface = {
        "configs": {
            "levotel_hotel_id": "test-api-key"
        }
    }

    interface_config = {
        "base_url": "https://test-vendor.com"
    }

    integration = LevotelWifiIntegration(interface, interface_config)

    with patch('app.integrations.wifi.levotel.LevotelWifiIntegration.handle_guest_checkin', new_callable=AsyncMock) as mock_checkin, \
         patch('app.integrations.wifi.levotel.LevotelWifiIntegration.handle_guest_checkout', new_callable=AsyncMock) as mock_checkout, \
         patch('app.integrations.wifi.levotel.LevotelWifiIntegration.handle_guest_room_change', new_callable=AsyncMock) as mock_room_change, \
         patch('app.integrations.wifi.levotel.LevotelWifiIntegration.handle_guest_name_update', new_callable=AsyncMock) as mock_name_update:

        # Test GUEST_CHECKIN
        await integration.handle_pms_message({"method": "GUEST_CHECKIN"})
        mock_checkin.assert_called_once()

        # Test GUEST_CHECKOUT
        await integration.handle_pms_message({"method": "GUEST_CHECKOUT"})
        mock_checkout.assert_called_once()

        # Test GUEST_ROOM_CHANGE
        await integration.handle_pms_message({"method": "GUEST_ROOM_CHANGE"})
        mock_room_change.assert_called_once()

        # Test GUEST_NAME_UPDATE
        await integration.handle_pms_message({"method": "GUEST_NAME_UPDATE"})
        mock_name_update.assert_called_once()

        # Test unsupported message type
        await integration.handle_pms_message({"method": "UNSUPPORTED"})
        # No additional assertions needed, just verify it doesn't raise an exception


# Tests for error handling functionality

@pytest.mark.asyncio
async def test_parse_response_content_dict():
    """Test parsing response when it's already a dict"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    response_data = {"checkin": "Success", "Message": "OK"}
    result = integration._parse_response_content(response_data)
    assert result == response_data


@pytest.mark.asyncio
async def test_parse_response_content_json_string():
    """Test parsing response when it's a JSON string"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    response_data = '{"checkin": "Success", "Message": "OK"}'
    result = integration._parse_response_content(response_data)
    assert result == {"checkin": "Success", "Message": "OK"}


@pytest.mark.asyncio
async def test_parse_response_content_invalid_json():
    """Test parsing response when it's an invalid JSON string"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    response_data = "invalid json"
    with patch('app.integrations.wifi.levotel.logger') as mock_logger:
        result = integration._parse_response_content(response_data)
        assert result == {}
        mock_logger.error.assert_called_once_with(f"Failed to parse response as JSON: {response_data}")


@pytest.mark.asyncio
async def test_parse_response_content_unexpected_type():
    """Test parsing response when it's an unexpected type"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    response_data = 123
    with patch('app.integrations.wifi.levotel.logger') as mock_logger:
        result = integration._parse_response_content(response_data)
        assert result == {}
        mock_logger.error.assert_called_once_with(f"Unexpected response type: {type(response_data)}")


@pytest.mark.asyncio
async def test_check_for_failure_response_success():
    """Test that no exception is raised for successful responses"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    parsed_response = {"checkin": "Success", "Message": "OK"}
    # Should not raise any exception
    integration._check_for_failure_response(parsed_response, "Checkin")


@pytest.mark.asyncio
async def test_check_for_failure_response_ignored_error():
    """Test that ignored errors are logged but not raised"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    parsed_response = {"checkin": "Failure", "Message": "Already Checkedin"}

    with patch('app.integrations.wifi.levotel.logger') as mock_logger:
        # Should not raise exception but should log info
        integration._check_for_failure_response(parsed_response, "Checkin")
        mock_logger.info.assert_called_once_with("Ignoring Levotel Checkin failure: Already Checkedin")


@pytest.mark.asyncio
async def test_check_for_failure_response_raises_exception():
    """Test that non-ignored errors raise LevotelIntegrationException"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    parsed_response = {"checkout": "Failure", "Message": "Some other error"}

    with patch('app.integrations.wifi.levotel.logger') as mock_logger:

        with pytest.raises(LevotelIntegrationException) as exc_info:
            integration._check_for_failure_response(parsed_response, "Checkout")

        # Verify exception details
        exception = exc_info.value
        assert exception.operation == "Checkout"
        assert exception.message == "Some other error"
        assert exception.response_data == parsed_response

        # Verify logging (no Sentry capture anymore)
        mock_logger.warning.assert_called_once_with(
            f"Levotel Checkout failed: Some other error, Response: {parsed_response}"
        )


@pytest.mark.asyncio
async def test_send_message_to_vendor_with_failure_response():
    """Test send_message_to_vendor with failure response from server"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Mock HTTP response with failure
    failure_response = '{"checkin": "Failure", "Message": "Some error"}'

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request:

        mock_request.return_value = failure_response

        payload = {"op": "checkin", "apikey": "test-api-key", "RN": "101", "GN": "Doe"}

        with pytest.raises(LevotelIntegrationException) as exc_info:
            await integration.send_message_to_vendor("GUEST_CHECKIN", payload)

        # Verify exception details
        exception = exc_info.value
        assert exception.operation == "Checkin"
        assert exception.message == "Some error"


@pytest.mark.asyncio
async def test_send_message_to_vendor_with_ignored_failure():
    """Test send_message_to_vendor with ignored failure response"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Mock HTTP response with ignored failure
    failure_response = '{"checkin": "Failure", "Message": "Already Checkedin"}'

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request, \
         patch('app.integrations.wifi.levotel.logger') as mock_logger:

        mock_request.return_value = failure_response

        payload = {"op": "checkin", "apikey": "test-api-key", "RN": "101", "GN": "Doe"}

        # Should not raise exception
        result = await integration.send_message_to_vendor("GUEST_CHECKIN", payload)

        # Should return the original response
        assert result == failure_response

        # Should log info about ignoring the error
        mock_logger.info.assert_called_once_with("Ignoring Levotel Checkin failure: Already Checkedin")


@pytest.mark.asyncio
async def test_send_message_to_vendor_with_success_response():
    """Test send_message_to_vendor with success response"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Mock HTTP response with success
    success_response = '{"checkin": "Success", "Message": "OK"}'

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request:
        mock_request.return_value = success_response

        payload = {"op": "checkin", "apikey": "test-api-key", "RN": "101", "GN": "Doe"}

        # Should not raise exception
        result = await integration.send_message_to_vendor("GUEST_CHECKIN", payload)

        # Should return the original response
        assert result == success_response


@pytest.mark.asyncio
async def test_levotel_integration_exception_should_be_ignored():
    """Test LevotelIntegrationException.should_be_ignored() method"""
    # Test ignored messages
    exception1 = LevotelIntegrationException("Checkin", "Already Checkedin")
    assert exception1.should_be_ignored() is True

    exception2 = LevotelIntegrationException("Checkout", "Guest Details not found")
    assert exception2.should_be_ignored() is True

    # Test non-ignored message
    exception3 = LevotelIntegrationException("Checkin", "Some other error")
    assert exception3.should_be_ignored() is False


@pytest.mark.asyncio
async def test_levotel_integration_exception_str():
    """Test LevotelIntegrationException string representation"""
    exception = LevotelIntegrationException("Checkin", "Test error", {"test": "data"})

    assert str(exception) == "LevotelIntegrationException: Checkin failed with message: Test error"
    assert exception.operation == "Checkin"
    assert exception.message == "Test error"
    assert exception.response_data == {"test": "data"}


@pytest.mark.asyncio
async def test_integration_text_content_type_ignored_failures():
    """Integration test with text content type for ignored failures"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Test "Already Checkedin" failure response as text
    failure_response_text = '{"Checkin":"Failure","Code":0,"Message":"Already Checkedin"}'

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request, \
         patch('app.integrations.wifi.levotel.logger') as mock_logger:

        mock_request.return_value = failure_response_text

        payload = {"op": "checkin", "apikey": "test-api-key", "RN": "101", "GN": "Doe"}

        # Should not raise exception
        result = await integration.send_message_to_vendor("GUEST_CHECKIN", payload)
        assert result == failure_response_text
        mock_logger.info.assert_called_once_with("Ignoring Levotel Checkin failure: Already Checkedin")


@pytest.mark.asyncio
async def test_integration_text_content_type_guest_not_found():
    """Integration test with text content type for 'Guest Details not found' failure"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Test "Guest Details not found" failure response as text
    failure_response_text = '{"Checkout":"Failure","Code":0,"Message":"Guest Details not found"}'

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request, \
         patch('app.integrations.wifi.levotel.logger') as mock_logger:

        mock_request.return_value = failure_response_text

        payload = {"op": "checkout", "apikey": "test-api-key", "RN": "101", "GN": "Doe"}

        # Should not raise exception
        result = await integration.send_message_to_vendor("GUEST_CHECKOUT", payload)
        assert result == failure_response_text
        mock_logger.info.assert_called_once_with("Ignoring Levotel Checkout failure: Guest Details not found")


@pytest.mark.asyncio
async def test_integration_json_content_type_failure():
    """Integration test with JSON content type for non-ignored failure"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Test failure response as dict (JSON parsed)
    failure_response_dict = {"Checkin": "Failure", "Code": 0, "Message": "Some other error"}

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request:

        mock_request.return_value = failure_response_dict

        payload = {"op": "checkin", "apikey": "test-api-key", "RN": "101", "GN": "Doe"}

        with pytest.raises(LevotelIntegrationException) as exc_info:
            await integration.send_message_to_vendor("GUEST_CHECKIN", payload)

        # Verify exception details
        exception = exc_info.value
        assert exception.operation == "Checkin"
        assert exception.message == "Some other error"
        assert exception.response_data == failure_response_dict


@pytest.mark.asyncio
async def test_integration_json_content_type_success():
    """Integration test with JSON content type for success response"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Test success response as dict (JSON parsed)
    success_response_dict = {"Checkin": "Success", "Code": 0, "Message": "OK"}

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request:
        mock_request.return_value = success_response_dict

        payload = {"op": "checkin", "apikey": "test-api-key", "RN": "101", "GN": "Doe"}

        # Should not raise exception
        result = await integration.send_message_to_vendor("GUEST_CHECKIN", payload)
        assert result == success_response_dict


@pytest.mark.asyncio
async def test_handle_guest_room_change_with_checkout_failure():
    """Test that room change continues with checkin even if checkout fails"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Mock checkout failure and checkin success
    checkout_failure = '{"checkout": "Failure", "Message": "Guest Details not found"}'
    checkin_success = '{"checkin": "Success", "Message": "OK"}'

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request:
        # First call (checkout) fails, second call (checkin) succeeds
        mock_request.side_effect = [checkout_failure, checkin_success]

        payload = {
            "old_room_number": "101",
            "room_number": "102",
            "guest_last_name": "Doe",
            "guest_first_name": "John"
        }

        # Should not raise exception, should complete successfully
        await integration.handle_guest_room_change(payload)

        # Verify both checkout and checkin were called
        assert mock_request.call_count == 2


@pytest.mark.asyncio
async def test_handle_guest_name_update_with_checkout_failure():
    """Test that name update continues with checkin even if checkout fails"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Mock checkout failure and checkin success
    checkout_failure = '{"checkout": "Failure", "Message": "Guest Details not found"}'
    checkin_success = '{"checkin": "Success", "Message": "OK"}'

    with patch.object(HttpClient, '_request', new_callable=AsyncMock) as mock_request:
        # First call (checkout) fails, second call (checkin) succeeds
        mock_request.side_effect = [checkout_failure, checkin_success]

        payload = {
            "room_number": "101",
            "old_guest_last_name": "Smith",
            "old_guest_first_name": "Jane",
            "guest_last_name": "Doe",
            "guest_first_name": "John"
        }

        # Should not raise exception, should complete successfully
        await integration.handle_guest_name_update(payload)

        # Verify both checkout and checkin were called
        assert mock_request.call_count == 2


@pytest.mark.asyncio
async def test_check_for_failure_response_early_return_on_success():
    """Test that the method returns early when operation is not a failure"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Test with success response
    parsed_response = {"checkin": "Success", "Message": "OK"}

    with patch('app.integrations.wifi.levotel.logger') as mock_logger:
        # Should return early without any logging or exceptions
        result = integration._check_for_failure_response(parsed_response, "Checkin")
        assert result is None

        # No logging should occur for success
        mock_logger.warning.assert_not_called()
        mock_logger.info.assert_not_called()


@pytest.mark.asyncio
async def test_check_for_failure_response_early_return_on_missing_operation():
    """Test that the method returns early when operation key is not in response"""
    interface = {"configs": {"levotel_hotel_id": "test-api-key"}}
    interface_config = {"base_url": "https://test-vendor.com"}
    integration = LevotelWifiIntegration(interface, interface_config)

    # Test with response that doesn't contain the operation key
    parsed_response = {"status": "OK", "Message": "Success"}

    with patch('app.integrations.wifi.levotel.logger') as mock_logger:
        # Should return early without any logging or exceptions
        result = integration._check_for_failure_response(parsed_response, "Checkin")
        assert result is None

        # No logging should occur
        mock_logger.warning.assert_not_called()
        mock_logger.info.assert_not_called()
