request ={
"hotelId":[
"0000164","0001263","0001661","0001825","0002365","0003211","0003233","0004701","0005044","0005435"
],
"fromDate":"2019-02-15T00:00:00.000Z",
"toDate":"2019-02-20T00:00:00.000Z",
"policy":[
"rp",
"nrp"
],
"mode":"TRANSACTION",
"application":"website",
"applyCoupon":"true",
"channel":"direct",
"subChannel":"trivago",
"utm":{
"source":"website"
},
"customerInfo":{
},
"quote":[
{
"quoteId":"3",
"skus":[
{
"skuCode":"48",
"quantity":1
},
{
"skuCode":"49",
"quantity":2
},
{
"skuCode":"50",
"quantity":1
}
]
},
{
"quoteId":"4",
"skus":[
{
"skuCode":"54",
"quantity":1
},
{
"skuCode":"57",
"quantity":1
}
]
}
]
}
app.handler.pricing_handler(request, None)
