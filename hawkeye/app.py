import os
import time

import sentry_sdk
from fastapi import FastAPI, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi_health import health
from sqladmin import Admin
from hawkeye.constants.hawkeye_constant import AUTHENTICATION_SECRET_KEY
from hawkeye.infrastructure.database import db_engine
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.flask import before_request

from hawkeye.admin.admin_views import get_admin_views
from hawkeye.admin.authentication import AdminAuthBackend
from hawkeye.api.health_check import check_db_health
from hawkeye.api.router import api_router
from hawkeye.common.exceptions import BaseException
from hawkeye.config.logging_conf import configure_logging
from hawkeye.infrastructure.cache import cache
from hawkeye.infrastructure.telemetry.otel_config import initialize_telemetry


def init_sentry():
    environment = os.environ.get('APP_ENV', 'local')
    sentry_sdk.init(
        release=os.environ.get('BUILD_NUMBER'),
        environment=environment,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production,
        traces_sample_rate=0,
    )
    sentry_sdk.set_tag('AWS_REGION', os.environ.get('AWS_REGION'))
    sentry_sdk.set_tag('CLUSTER_IDENTIFIER', os.environ.get('CLUSTER_IDENTIFIER'))


def create_app() -> FastAPI:
    init_sentry()
    app = FastAPI()

    # Initialize OpenTelemetry tracing
    initialize_telemetry(app)

    app.include_router(router=api_router, prefix="/hawkeye")
    app.add_api_route("/health", health([check_db_health]))
    app.add_exception_handler(BaseException, handler=base_exception_handler)
    app.add_exception_handler(RequestValidationError, handler=validation_exception_handler)
    setup_admin(app)
    cache.setup_tenant_cache()

    @app.middleware("http")
    async def add_process_time_header(request: Request, call_next):
        start_time = time.time()
        before_request(request)
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response

    configure_logging()
    return app


def validation_exception_handler(request: Request, error) -> JSONResponse:
    message = ", ".join([e.get("msg") for e in error.errors()])
    return JSONResponse({"detail": message}, status_code=400)


def base_exception_handler(request: Request, error) -> JSONResponse:
    return JSONResponse({"detail": error.message}, status_code=error.status_code)


def setup_admin(app):
    tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())
    engine = db_engine.get_engine(tenant_id=tenant_id)
    authentication_backend = AdminAuthBackend(tenant_id, secret_key=AUTHENTICATION_SECRET_KEY)
    admin = Admin(app, engine, base_url='/hawkeye/admin', title='Hawkeye Admin', authentication_backend=authentication_backend,
                  templates_dir="hawkeye/templates")

    views = get_admin_views()
    for view in views:
        admin.add_view(view)
