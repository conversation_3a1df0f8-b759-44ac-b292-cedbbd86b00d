from pydantic import NonNegativeInt, BaseModel, root_validator, constr


class PricePushRequest(BaseModel):
    hotel_ids: constr(strip_whitespace=True)
    abw_start: NonNegativeInt
    abw_end: NonNegativeInt

    @root_validator(pre=False)
    def validate_date(cls, values):
        if values['hotel_ids'] == '':
            raise ValueError("Provide hotel_ids to trigger price calculation")
        hotel_ids = values['hotel_ids'].split(",")
        if len(hotel_ids) > 100:
            raise ValueError("Maximum of 100 hotels per batch are allowed in the list.")
        if 'abw_start' in values and 'abw_end' in values \
                and not (0 <= values['abw_end'] - values['abw_start'] < 31):
            raise ValueError("ABW end should be greater than ABW start and not more than 30 from ABW start")
        return values

    def strip_commas(self):
        """
        removing leading and trailing commas
        """
        self.hotel_ids = self.hotel_ids.lstrip(",")
        self.hotel_ids = self.hotel_ids.rstrip(",")
