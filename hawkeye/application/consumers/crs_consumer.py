import logging

from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient

from hawkeye.application.decorators import request_middleware
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.application.services.dtos.inventory_dtos import RoomTypeInventoryDto
from hawkeye.application.services.inventory_service import InventoryService
from hawkeye.application.services.price_calculation_service import PriceCalculationService
from hawkeye.constants.hawkeye_constant import CrsEventType, CrsEventEntity
from hawkeye.domain.factory.price_calculation_trigger_factory import PriceCalculationTriggerFactory
from hawkeye.infrastructure.consumers.base_consumer import BaseRMQConsumer
from hawkeye.infrastructure.consumers.consumer_config import CrsConsumerConfig
from hawkeye.infrastructure.telemetry.decorators import background_task
from hawkeye.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


class CrsServiceConsumer(BaseRMQConsumer):
    def __init__(
        self,
        catalog_service: CatalogService,
        inventory_service: InventoryService,
        price_calculation_service: PriceCalculationService,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(CrsConsumerConfig(tenant_id))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.catalog_service = catalog_service
        self.inventory_service = inventory_service
        self.price_calculation_service = price_calculation_service

    @request_middleware
    @serverless_function
    @background_task(name='start_crs_consumer')
    def process_message(self, consumer_event, message):
        logger.info("CRS process message called for message: %s", consumer_event.get("message_id"))
        event_type = consumer_event.get("event_type", "")

        inventory_dtos = []
        try:
            if event_type == CrsEventType.INVENTORY_ROOM_TYPE_UPDATED.value:
                inventory_dtos = self.handle_inventory_update_event(consumer_event)
            elif event_type == CrsEventType.HOUSEKEEPING_RECORD_UPDATED.value:
                 inventory_dtos = self.handle_housekeeping_update_event(consumer_event)
            elif CrsEventType.INVENTORY_DNR.value in event_type:
                inventory_dtos = self.handle_inventory_dnr_event(consumer_event)

            price_calculation_triggers = PriceCalculationTriggerFactory \
                .create_price_calculation_triggers_from_inventory_dtos(inventory_dtos)
            for price_calculation_trigger in price_calculation_triggers:
                self.price_calculation_service.trigger_price_calculation(
                    config_date_time=None,
                    price_calculation_trigger=price_calculation_trigger,
                )

        except Exception as exc:
            logger.info(f"Error in consuming the messages from crs consumer: {exc}")
            message.reject()
            return

        message.ack()
        logger.info("Crs message process complete. Message acknowledged")

    def _get_room_type_inventory_dtos(self, consumer_event, should_increment_dnr=None):
        inventory_updates = list()
        dnr_payload = None
        for event in consumer_event["events"]:
            if event["entity_name"] == CrsEventEntity.ROOM_TYPE_INVENTORY.value:
                inventory_updates = event["payload"]
            elif event["entity_name"] == CrsEventEntity.DNR.value:
                dnr_payload = event["payload"]

        if not inventory_updates:
            return []

        room_type_inventory_dtos = []

        for data in inventory_updates:
            if not self.catalog_service.does_hotel_exist(data["hotel_id"]):
                return
            room_type_inventory_dtos.append(RoomTypeInventoryDto.create_from_inventory_data(
                data,
                should_increment_dnr,
                consumer_event["event_type"],
                dnr_payload,
            ))

        return room_type_inventory_dtos

    def handle_inventory_update_event(self, inventory_update_event):
        room_type_inventory_dtos = self._get_room_type_inventory_dtos(inventory_update_event)
        for chunk in chunks(room_type_inventory_dtos, 50):
            self.inventory_service.save_room_type_inventories(chunk)
        return room_type_inventory_dtos

    def handle_housekeeping_update_event(self, housekeeping_update_event):
        # Ignoring for now for out of service status
        return []

    def handle_inventory_dnr_event(self, inventory_dnr_event):
        if inventory_dnr_event["event_type"] == CrsEventType.DNR_REMOVED.value:
            # This event is for past dates. Can be ignored.
            return

        should_increment_dnr = False if inventory_dnr_event["event_type"] == CrsEventType.DNR_RELEASED.value else True

        room_type_inventory_dtos = self._get_room_type_inventory_dtos(inventory_dnr_event, should_increment_dnr)
        for chunk in chunks(room_type_inventory_dtos, 50):
            self.inventory_service.save_room_type_inventories(chunk)
        return room_type_inventory_dtos
