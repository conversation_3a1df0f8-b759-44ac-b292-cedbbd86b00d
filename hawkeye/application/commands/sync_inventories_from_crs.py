#!/usr/bin/env python

import logging

import click
from hawkeye.infrastructure.database.db_engine import setup_tenant_sessions
from treebo_commons.multitenancy.tenant_client import TenantClient

from hawkeye.app import create_app
from hawkeye.application.services.inventory_service import InventoryService
from hawkeye.globals import worker_context
from hawkeye.utils.collectionutils import chunks
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    "--hotel_ids",
    help="Hotel IDs for which this command should run.",
    type=str,
    required=True,
)
@click.option(
    "--from_date",
    help="From date for which properties should be synced",
    type=str,
    required=True,
)
@click.option(
    "--to_date",
    help="End date till which properties should be synced",
    type=str,
    required=True,
)
@inject(inventory_service=InventoryService)
def sync_inventory_from_crs(tenant_id, hotel_ids, from_date, to_date, inventory_service):
    click.echo(f"Tenant ID: {tenant_id}")
    setup_tenant_sessions(tenant_id=tenant_id)
    worker_context.set_tenant_id(tenant_id)
    hotel_ids = hotel_ids.split(",") if hotel_ids else []
    for chunk in chunks(hotel_ids, 20):
        inventory_service.sync_inventory_from_crs(chunk, from_date=from_date, to_date=to_date)


if __name__ == '__main__':
    app = create_app()
    sync_inventory_from_crs()
