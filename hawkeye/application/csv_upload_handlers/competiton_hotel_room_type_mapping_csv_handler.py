import codecs
import csv
import logging
from collections import defaultdict

import sentry_sdk

from hawkeye.application.csv_upload_handlers.dtos.competitive_hotel_room_type_mapping_dto import (
    CompetitiveHotelRoomTypeMappingDTO,
)
from hawkeye.application.decorators import session_manager
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus
from hawkeye.domain.entities.rule_file_upload import RuleFileUpload
from hawkeye.domain.factory.competitive_hotel_room_type_mapping import (
    CompetitiveHotelRoomTypeMappingFactory,
)
from hawkeye.infrastructure.exception import InvalidFileTypeError, InvalidHotelIdError, InvalidRoomTypeError
from hawkeye.infrastructure.repositories.competitve_hotel_mapping import (
    CompetitiveHotelMappingRepository,
)
from hawkeye.infrastructure.repositories.competitve_hotel_room_type_mapping_repository import (
    CompetitiveHotelRoomTypeMappingRepository,
)
from hawkeye.infrastructure.repositories.rule_file_repository import RuleFileRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CompetitiveHotelMappingRepository,
        CompetitiveHotelRoomTypeMappingRepository,
        RuleFileRepository,
        CatalogService,
    ]
)
class CompetitiveHotelRoomTypeMappingCSVHandler:
    required_csv_columns = [
        "competitive_hotel_id",
        "competitive_hotel_room_type",
        "internal_hotel_id",
        "internal_hotel_room_type",
    ]

    def __init__(
        self,
        competitive_hotel_mapping_repository,
        competitive_hotel_room_type_mapping_repository,
        rule_file_repository,
        catalog_service,
    ):
        self.competitive_hotel_mapping_repository = competitive_hotel_mapping_repository
        self.competitive_hotel_room_type_mapping_repository = (
            competitive_hotel_room_type_mapping_repository
        )
        self.rule_file_repository = rule_file_repository
        self.catalog_service = catalog_service

    def validate_uploaded_file(self, uploaded_file):
        if not uploaded_file.filename.endswith(".csv"):
            raise InvalidFileTypeError
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_columns = csv_file.fieldnames
        missing_columns = [
            column for column in self.required_csv_columns if column not in file_columns
        ]
        if missing_columns:
            raise Exception(
                f"Missing columns: {', '.join(missing_columns)}. Please fix the CSV and then upload."
            )

    def process_file(self, uploaded_file, user_email):
        self.validate_uploaded_file(uploaded_file)
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_id = self.save_rule_file(uploaded_file, user_email)
        failed_rows = []
        try:
            failed_rows = self.process_rules(csv_file, file_id)
        except Exception as e:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.FAILED)
            sentry_sdk.capture_exception(e)
            raise e
        else:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.SUCCESS)
        return failed_rows

    @session_manager(commit=True)
    def save_rule_file(self, uploaded_file, user_email):
        rule_file = RuleFileUpload(
            file_name=uploaded_file.filename,
            uploaded_by=user_email,
            status=RuleFileUploadStatus.PENDING,
            path="",
        )
        return self.rule_file_repository.save(rule_file)

    @session_manager(commit=True)
    def update_rule_file_status(self, file_id, status):
        rule_file = self.rule_file_repository.load_for_update(file_id)
        rule_file.status = status
        self.rule_file_repository.update_rule_file(rule_file)

    def process_rules(self, csv_file, file_id):
        failed_rows = []
        room_type_mapping_rule_dtos_by_hotel_ids = defaultdict(list)
        valid_hotels = self.catalog_service.get_hotel_ids()
        hotel_ids_to_row = defaultdict(list)
        for row in csv_file:
            try:
                row.update(file_id=file_id)
                dto = CompetitiveHotelRoomTypeMappingDTO(**row)

                key = (dto.internal_hotel_id, dto.competitive_hotel_id)
                hotel_ids_to_row[key].append(csv_file.line_num - 1)
                if len(hotel_ids_to_row[key]) > 1:
                    continue
                if dto.internal_hotel_id not in valid_hotels:
                    raise InvalidHotelIdError(description=f"{dto.internal_hotel_id} is invalid.")
                valid_room_types = [room_type.room_type_name.upper() for room_type in
                                    self.catalog_service.get_active_room_types(dto.internal_hotel_id)]
                if dto.internal_hotel_room_type.upper() not in valid_room_types:
                    raise InvalidRoomTypeError(description=f"Room type: {dto.internal_hotel_room_type} is invalid")
                competition_hotel_mappings = (
                    self.competitive_hotel_mapping_repository.load_all_mappings(
                        dto.internal_hotel_id
                    )
                )
                if competition_hotel_mappings:
                    for mapping in competition_hotel_mappings:
                        if mapping.competitive_hotel_id == dto.competitive_hotel_id:
                            break
                    else:
                        raise Exception(
                            f"For hotel id {dto.internal_hotel_id}, competition hotel id {dto.competitive_hotel_id} does not exist. Please create hotel mapping first."
                        )
                else:
                    raise Exception(
                        f"No competition hotels mappings Exist for hotel id : {dto.internal_hotel_id}. For creating competition hotel room type mappings, competition hotel should exist first."
                    )
                dto.file_id = file_id
                key = (dto.internal_hotel_id, dto.competitive_hotel_id)
                room_type_mapping_rule_dtos_by_hotel_ids[key].append(dto)
            except Exception as e:
                failed_rows.append(dict(row=csv_file.line_num - 1, error=str(e)))
                sentry_sdk.capture_exception(e)

        for (internal_hotel_id, competitive_hotel_id), row_numbers in hotel_ids_to_row.items():
            if len(row_numbers) > 1:
                for row_number in row_numbers:
                    failed_rows.append(
                        dict(
                            row=row_number,
                            error=f"Duplicate row found for internal hotel_id {internal_hotel_id} and competitive hotel id {competitive_hotel_id}.",
                        )
                    )
        self.save_rules(room_type_mapping_rule_dtos_by_hotel_ids)
        return failed_rows

    @session_manager(commit=True)
    def save_rules(self, room_type_mapping_rule_dtos_by_hotel_ids):
        for (internal_hotel_id, competitive_hotel_id), mapping_rule_dtos in room_type_mapping_rule_dtos_by_hotel_ids.items():
            existing_room_type_mappings = (
                self.competitive_hotel_room_type_mapping_repository.load_all_mappings(internal_hotel_id, competitive_hotel_id)
            )
            for mapping in existing_room_type_mappings:
                mapping.mark_inactive()

            if existing_room_type_mappings:
                self.competitive_hotel_room_type_mapping_repository.update_all(
                    existing_room_type_mappings
                )
            competitive_hotel_room_type_mappings = [
                CompetitiveHotelRoomTypeMappingFactory.create_competitive_hotel_room_type_mapping(
                    rule_dto
                )
                for rule_dto in mapping_rule_dtos
            ]
            self.competitive_hotel_room_type_mapping_repository.create_all(
                competitive_hotel_room_type_mappings
            )
