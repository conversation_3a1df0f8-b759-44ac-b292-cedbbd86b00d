import codecs
import csv
from collections import defaultdict

import sentry_sdk
from treebo_commons.utils import dateutils

from hawkeye.api.rules.schemas import RuleFileCsvRequest
from hawkeye.application.decorators import session_manager
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.application.services.dtos.pricing_rule_dtos import PricingRuleDto
from hawkeye.application.services.rule_application_service import RuleApplicationService
from hawkeye.common.slack_alert_helpers import SlackAlert
from hawkeye.config import rule_file_upload_conf as config
from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus, RuleType
from hawkeye.domain.entities.rule_file_upload import RuleFileUpload
from hawkeye.domain.factory.base_room_type_price_rule_factory import (
    BaseRoomTypePriceRuleFactory,
)
from hawkeye.domain.factory.base_room_type_sku_price_rule_factory import BaseRoomTypeSkuPriceRuleFactory
from hawkeye.infrastructure.exception import InvalidFileTypeError, InvalidHotelIdError
from hawkeye.infrastructure.repositories.base_room_type_price_rule_repository import (
    BaseRoomTypePriceRuleRepository,
)
from hawkeye.infrastructure.repositories.base_room_type_sku_price_rule_repository import (
    BaseRoomTypeSkuPriceRuleRepository,
)
from hawkeye.infrastructure.repositories.hotel_config_repository import (
    HotelConfigRepository,
)
from hawkeye.infrastructure.repositories.rule_file_repository import RuleFileRepository
from object_registry import locate_instance, register_instance


@register_instance(
    dependencies=[
        HotelConfigRepository,
        RuleFileRepository,
        BaseRoomTypePriceRuleRepository,
        BaseRoomTypeSkuPriceRuleRepository,
        RuleApplicationService,
    ]
)
class RuleFileCSVHandler:
    required_csv_columns = [
        "rule_type",
        "start_range",
        "end_range",
        "multiplier",
        "addition",
        "start_price",
        "end_price",
    ]

    def __init__(
        self,
        hotel_config_repository,
        rule_file_repository,
        base_room_type_price_rule_repository,
        base_room_type_sku_price_rule_repository,
        rule_application_service,
    ):
        self.rule_application_service = rule_application_service
        self.base_room_type_price_rule_repository = base_room_type_price_rule_repository
        self.base_room_type_sku_price_rule_repository = base_room_type_sku_price_rule_repository
        self.hotel_config_repository = hotel_config_repository
        self.rule_file_repository = rule_file_repository

    def validate_uploaded_file(self, uploaded_file):
        if not uploaded_file.filename.endswith(".csv"):
            raise InvalidFileTypeError
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_columns = csv_file.fieldnames
        missing_columns = [
            column for column in self.required_csv_columns if column not in file_columns
        ]
        if missing_columns:
            raise Exception(
                f"Missing columns: {', '.join(missing_columns)}. Please fix the CSV and then upload."
            )

    def process_file(self, uploaded_file, user_email):
        self.validate_uploaded_file(uploaded_file)
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_id = self.save_rule_file(uploaded_file, user_email)
        csv_file_data = []
        catalog_service = locate_instance(CatalogService)
        valid_hotels = catalog_service.get_hotel_ids()
        skip_hotels = set()
        hotel_id_to_rule_type = defaultdict(list)
        failed_rows = []
        error_messages = []
        is_sku_rule_file = "sku" in csv_file.fieldnames
        unique_tuple = set()
        parameters = list(config.CSV_COLUMNS._asdict().values())
        for rows in csv_file:
            if not rows:
                continue
            try:
                hotel_id = rows["hotel_ID"].replace(" ", "")
                if hotel_id in skip_hotels:
                    continue

                if hotel_id not in valid_hotels:
                    raise InvalidHotelIdError(description=f"{hotel_id} is invalid.")

                data = {
                    config.CSV_COLUMNS.hotel_id: hotel_id,
                    config.CSV_COLUMNS.rule_type: rows["rule_type"],
                    config.CSV_COLUMNS.start_range: rows["start_range"],
                    config.CSV_COLUMNS.end_range: rows["end_range"],
                    config.CSV_COLUMNS.multiplier: rows["multiplier"],
                    config.CSV_COLUMNS.addition: rows["addition"],
                    config.CSV_COLUMNS.start_price: rows["start_price"],
                    config.CSV_COLUMNS.end_price: rows["end_price"],
                }
                if is_sku_rule_file:
                    data[config.CSV_COLUMNS.sku] = rows["sku"]
                    data[config.CSV_COLUMNS.input_type] = rows["input_type"]
                data = RuleFileCsvRequest(**data)
                row_details = list()
                for params in parameters:
                    if params == "hotel_id":
                        row_details.append(hotel_id)
                    elif params not in rows:
                        continue
                    else:
                        row_details.append(str(rows[params]).lower().strip())

                info_tuple = tuple(row_details)
                if info_tuple in unique_tuple:
                    continue

                unique_tuple.add(info_tuple)
                hotel_id_to_rule_type[hotel_id].append(data.dict())

            except Exception as e:
                failed_rows.append(dict(row=csv_file.line_num - 1, error=str(e)))
                skip_hotels.add(rows["hotel_ID"])
                error_message = (
                    f"File: {uploaded_file.filename} (row={csv_file.line_num - 1}). "
                    f"Rule type  {rows['rule_type']} not properly configured for "
                    f"hotel {rows['hotel_ID']}. Error: {e}"
                )
                error_messages.append(error_message)

        if error_messages:
            full_error_message = '\n'.join(msg for msg in error_messages)
            SlackAlert.send_alert(full_error_message)

        for hotel_id in skip_hotels:
            hotel_id_to_rule_type.pop(hotel_id, None)

        for data in hotel_id_to_rule_type.values():
            csv_file_data.extend(data)

        try:
            if is_sku_rule_file:
                self.process_sku_rules(file_id, csv_file_data)
            else:
                self.process_rules(file_id, csv_file_data)
        except Exception as e:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.FAILED)
            raise e

        self.update_rule_file_status(file_id, RuleFileUploadStatus.SUCCESS)
        return failed_rows

    @session_manager(commit=True)
    def save_rule_file(self, uploaded_file, user_email):
        rule_file = RuleFileUpload(
            file_name=uploaded_file.filename,
            uploaded_by=user_email,
            status=RuleFileUploadStatus.PENDING,
            path=''
        )
        return self.rule_file_repository.save(rule_file)

    @session_manager(commit=True)
    def update_rule_file_status(self, file_id, status):
        rule_file = self.rule_file_repository.load_for_update(file_id)
        rule_file.status = status
        self.rule_file_repository.update_rule_file(rule_file)

    def process_rules(self, file_id, csv_file_data):
        hotel_ids = set()
        pricing_rule_dtos = []

        for data in csv_file_data:
            hotel_ids.add(data["hotel_id"])
            data["file_id"] = file_id
            dto = PricingRuleDto.create_from_data(data=data)
            if dto:
                pricing_rule_dtos.append(dto)

        rules = self.base_room_type_price_rule_repository.load_all(hotel_ids=list(hotel_ids))
        hotel_wise_rule_mapping = defaultdict(lambda: defaultdict(list))
        for rule in rules:
            hotel_wise_rule_mapping[rule.hotel_id][rule.rule_type].append(rule)

        update_dtos = set()
        update_target_date_dtos = set()

        for dto in pricing_rule_dtos:
            if dto.rule_type in hotel_wise_rule_mapping.get(dto.hotel_id, {}):
                if dto.rule_type == RuleType.TARGET_DATE.value:
                    update_target_date_dtos.add((
                        dto.rule_type, dto.hotel_id, dto.config.to_json()['target_date']
                    ))
                else:
                    update_dtos.add((dto.rule_type, dto.hotel_id))

        create_pricing_rules = [
            BaseRoomTypePriceRuleFactory.create_pricing_rule(dto)
            for dto in pricing_rule_dtos
        ]

        update_pricing_rules = []
        for rule_type, hotel_id, target_date in update_target_date_dtos:
            for rule in hotel_wise_rule_mapping[hotel_id][rule_type]:
                if dateutils.date_to_ymd_str(rule.config.target_date) == target_date:
                    rule.mark_delete()
                    update_pricing_rules.append(rule)

        for rule_type, hotel_id in update_dtos:
            for rule in hotel_wise_rule_mapping[hotel_id][rule_type]:
                rule.mark_delete()
                update_pricing_rules.append(rule)

        self.save_rules(file_id, create_pricing_rules, update_pricing_rules)

    def process_sku_rules(self, file_id, csv_file_data):
        hotel_ids = set()
        pricing_rule_dtos = []

        for data in csv_file_data:
            hotel_ids.add(data["hotel_id"])
            data["file_id"] = file_id
            dto = PricingRuleDto.create_from_data(data=data)
            if dto:
                pricing_rule_dtos.append(dto)

        rules = self.base_room_type_sku_price_rule_repository.load_all(hotel_ids=list(hotel_ids))
        hotel_wise_rule_mapping = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        for rule in rules:
            hotel_wise_rule_mapping[rule.hotel_id][rule.sku_code][rule.rule_type].append(rule)

        update_dtos = set()
        update_target_date_dtos = set()

        for dto in pricing_rule_dtos:
            if dto.rule_type in hotel_wise_rule_mapping.get(dto.hotel_id, {}).get(dto.sku_code, {}):
                if dto.rule_type == RuleType.TARGET_DATE.value:
                    update_target_date_dtos.add((
                        dto.rule_type, dto.hotel_id, dto.sku_code, dto.config.to_json()['target_date']
                    ))
                else:
                    update_dtos.add((dto.rule_type, dto.hotel_id, dto.sku_code))

        create_pricing_rules = [
            BaseRoomTypeSkuPriceRuleFactory.create_sku_pricing_rule(dto)
            for dto in pricing_rule_dtos
        ]

        update_pricing_rules = []
        for rule_type, hotel_id, sku_code, target_date in update_target_date_dtos:
            for rule in hotel_wise_rule_mapping[hotel_id][sku_code][rule_type]:
                if dateutils.date_to_ymd_str(rule.config.target_date) == target_date:
                    rule.mark_delete()
                    update_pricing_rules.append(rule)

        for rule_type, hotel_id, sku_code in update_dtos:
            for rule in hotel_wise_rule_mapping[hotel_id][sku_code][rule_type]:
                rule.mark_delete()
                update_pricing_rules.append(rule)

        self.save_rules(file_id, create_pricing_rules, update_pricing_rules, is_sku_rule_file=True)

    @session_manager(commit=True)
    def save_rules(self, file_id, create_pricing_rules, update_pricing_rules, is_sku_rule_file=False):
        if is_sku_rule_file:
            self.rule_application_service.save_rules(
                self.base_room_type_sku_price_rule_repository, create_pricing_rules, update_pricing_rules
            )
        else:
            self.rule_application_service.save_rules(
                self.base_room_type_price_rule_repository, create_pricing_rules, update_pricing_rules
            )
        self.update_rule_file_status(file_id, RuleFileUploadStatus.SUCCESS)
