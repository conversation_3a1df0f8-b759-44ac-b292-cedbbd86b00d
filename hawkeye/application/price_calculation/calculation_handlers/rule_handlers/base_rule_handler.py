import abc


class EndHandler(object):
    def __init__(self):
        ...

    def perform_calculation(self, price_aggregate, price_rules):
        return price_aggregate


class BaseRuleHandler(metaclass=abc.ABCMeta):
    def __init__(self):
        self.next_handler = <PERSON>Handler()

    @abc.abstractmethod
    def get_price_rule(self, price_aggregate, price_rules):
        raise NotImplementedError(
            "This method needs to be implemented by subclass"
        )

    def perform_calculation(self, price_aggregate, price_rules):
        base_room_type_price_rule = self.get_price_rule(price_aggregate, price_rules)

        if price_aggregate.room_type_price_entity and base_room_type_price_rule:
            price_aggregate.room_type_price_entity.apply_price_rule(base_room_type_price_rule)

        return self.next_handler.perform_calculation(price_aggregate, price_rules)
