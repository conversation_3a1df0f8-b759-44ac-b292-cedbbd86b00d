import logging
from decimal import Decimal
from typing import List

from hawkeye.domain.entities.room_type_price import RoomTypePrice
from hawkeye.domain.entities.hotel_room_inventory import HotelRoomInventory
from hawkeye.domain.entities.hotel_room_type import HotelRoomType
from hawkeye.domain.entities.base_room_type_price_rule import BaseRoomTypePriceRule
from hawkeye.domain.entities.incremental_price_rule import IncrementalPriceRule
from hawkeye.domain.entities.competitive_price_weightage import CompetitivePriceWeightage
from hawkeye.domain.entities.compset_pricing_threshold import CompsetPricingThreshold
from hawkeye.infrastructure.exception import (
    PriceAggregateInitializationError,
    RackRateMissingError,
    NegativeFinalPriceError,
)

logger = logging.getLogger(__name__)


class PriceAggregate(object):
    def __init__(
        self,
        hotel_id,
        target_date,
        price_trigger_id: str,
        base_room_type_price_rules,
        room_type_entities: List[HotelRoomType],
        room_type_inventory_entities: List[HotelRoomInventory],
        incremental_price_rules: List[IncrementalPriceRule],
        competitive_price_weightages: List[CompetitivePriceWeightage],
        compset_pricing_thresholds: List[CompsetPricingThreshold],
    ):
        self.hotel_id = hotel_id
        self.target_date = target_date
        self.price_trigger_id = price_trigger_id
        self.base_room_type_price_rules = base_room_type_price_rules
        self.room_type_entities = room_type_entities
        self.room_type_inventory_entities = room_type_inventory_entities
        self.incremental_price_rules = incremental_price_rules
        self.competitive_price_weightages = competitive_price_weightages
        self.compset_pricing_thresholds = compset_pricing_thresholds

        for room_type_entity in room_type_entities:
            if room_type_entity.is_base_room_type:
                self.base_room_type_entity = room_type_entity
                break
        else:
            raise PriceAggregateInitializationError

        self.room_type_price_entity = None
        self.incremental_price_entities = []
        self.price_alert = None

    def __str__(self):
        return f"""PriceAggregate for hotel {self.hotel_id} date {self.target_date}
                room_type {self.room_type_code}
                availability {self.hotel_inventory_availability_count}"""

    def __repr__(self):
        return f"""PriceAggregate(
            hotel_id={self.hotel_id},
            target_date={self.target_date},
            base_room_type_entity.room_type_code={self.room_type_code},
            room_type_inventory_entities.availability_count={self.hotel_inventory_availability_count}
            room_type_price={self.room_type_price_entity and self.room_type_price_entity.final_price}
        )"""

    @property
    def room_type_code(self):
        return self.base_room_type_entity.room_type_code

    @property
    def room_type_name(self):
        return self.base_room_type_entity.room_type_name

    @property
    def total_rooms(self):
        return sum([room_type_entity.total_rooms for room_type_entity in self.room_type_entities])

    @property
    def rack_rate(self):
        if self.base_room_type_entity.rack_rate:
            return self.base_room_type_entity.rack_rate
        raise RackRateMissingError

    @property
    def hotel_inventory_availability_count(self):
        availability_count = 0
        for hotel_room_inventory in self.room_type_inventory_entities:
            if hotel_room_inventory.availability_count > 0:
                availability_count += hotel_room_inventory.availability_count
        return availability_count

    @property
    def hotel_inventory_out_of_order(self):
        return sum([hotel_room_inventory.out_of_order for hotel_room_inventory in self.room_type_inventory_entities])

    @property
    def hotel_occupancy(self):
        total_rooms = self.total_rooms - self.hotel_inventory_out_of_order
        occupied_rooms = total_rooms - self.hotel_inventory_availability_count

        if occupied_rooms < 0 or total_rooms < 0:
            logger.info(f"Inconsistent inventories for hotel {self.hotel_id} and date {self.target_date}")
            return 0

        if total_rooms == 0:
            return 0

        return (occupied_rooms / total_rooms) * 100

    def finalize_price(self, room_type_price_entity: RoomTypePrice, occupancy=None):
        if room_type_price_entity.final_price < 0:
            raise NegativeFinalPriceError(room_type_price_entity.room_type)
        room_type_price_entity.final_price = float(f"{room_type_price_entity.final_price:.2f}")
        room_type_price_entity.price_trigger_id = self.price_trigger_id
        if not room_type_price_entity.sku_code:
            room_type_price_entity.populate_sku_code(occupancy)
        room_type_price_entity.populate_is_skipped()

    def set_room_type_price(self, room_type_price_entity: RoomTypePrice):
        self.room_type_price_entity = room_type_price_entity

    def add_incremental_room_type_price(self, incremental_price_entity: RoomTypePrice, occupancy=None):
        self.finalize_price(incremental_price_entity, occupancy)
        self.incremental_price_entities.append(incremental_price_entity)

    @property
    def applicable_incremental_price_rules(self):
        # Adding check to skip incremental-price-rule for base-room-type to avoid duplicate rates
        enabled_room_types = {
            room_type_entity.room_type_name for room_type_entity in self.room_type_entities
            if not room_type_entity.is_base_room_type
        }
        return [incremental_price_rule for incremental_price_rule in self.incremental_price_rules
                if incremental_price_rule.room_type in enabled_room_types]

    def has_inconsistent_room_inventory(self):
        total_rooms = self.total_rooms - self.hotel_inventory_out_of_order
        occupied_rooms = total_rooms - self.hotel_inventory_availability_count

        return occupied_rooms < 0 or total_rooms < 0

    def set_price_alert(self, price_alert):
        self.price_alert = price_alert
