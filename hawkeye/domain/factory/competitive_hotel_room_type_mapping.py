from hawkeye.domain.entities.competitive_hotel_room_type_mapping import (
    CompetitiveHotelRoomTypeMapping,
)


class CompetitiveHotelRoomTypeMappingFactory:
    @staticmethod
    def create_competitive_hotel_room_type_mapping(dto):
        return CompetitiveHotelRoomTypeMapping(
            internal_hotel_id=dto.internal_hotel_id,
            competitive_hotel_id=dto.competitive_hotel_id,
            competitive_hotel_room_type=dto.competitive_hotel_room_type,
            internal_hotel_room_type=dto.internal_hotel_room_type,
            file_id=dto.file_id,
        )
