import datetime
import random


def random_id_generator(prefix=None):
    """
    Returns: a random generated prefixed number
    """
    created_at = datetime.datetime.utcnow()
    part1 = created_at.strftime("%d%m%y")
    part2 = created_at.strftime("%H%M")

    number = random.randint(0, 999999999)
    part4 = int(number % 10000)
    number = (number - part4) / 10000
    part3 = int(number % 10000)
    part3 += (created_at.month * 10)
    if prefix:
        parts = [prefix, part1, part2, str(part3).rjust(3, '0'),
                 str(part4).rjust(4, '0')]
    else:
        parts = [part1, part2, str(part3).rjust(3, '0'),
                 str(part4).rjust(4, '0')]
    return '-'.join(parts)
