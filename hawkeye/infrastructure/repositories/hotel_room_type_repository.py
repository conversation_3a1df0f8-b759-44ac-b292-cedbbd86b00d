import logging
from typing import List

from hawkeye.domain.entities.hotel_room_type import HotelRoomType
from hawkeye.infrastructure.database.models import HotelRoomTypeModel
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.hotel_room_type_adaptor import HotelRoomTypeAdaptor
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class HotelRoomTypeRepository(BaseRepository):
    _model = HotelRoomTypeModel
    _adaptor = HotelRoomTypeAdaptor()

    def create(self, hotel_room_type: HotelRoomType):
        hotel_room_type_models = self._adaptor.to_db_entity(hotel_room_type)
        try:
            self._save(hotel_room_type_models)
        except Exception:
            raise DatabaseError

    def update(self, hotel_room_type: HotelRoomType):
        hotel_room_type_models = self._adaptor.to_db_entity(hotel_room_type)
        try:
            self._update(hotel_room_type_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def create_all(self, hotel_room_type_configs: List[HotelRoomType]):
        hotel_room_type_configs_models = [self._adaptor.to_db_entity(domain_entity=hotel_room_type_config)
                                          for hotel_room_type_config in hotel_room_type_configs]
        try:
            self._save_all(hotel_room_type_configs_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def load_for_update(self, hotel_id, room_type_code):
        return self._adaptor.to_domain_entity(
            self.get_for_update(HotelRoomTypeModel, hotel_id=hotel_id, room_type_code=room_type_code))

    def load_base_room_type(self, hotel_id):
        return self._adaptor.to_domain_entity(
            self.get(
                self._model,
                hotel_id=hotel_id,
                is_base_room_type=True,
                is_active=True
            )
        )

    def load_hotel_room_types(self, hotel_id, exclude_base_room_type=False):
        query = self.query(self._model).filter(
            self._model.hotel_id == hotel_id,
            self._model.is_active == True,
        )
        if exclude_base_room_type:
            query = query.filter(self._model.is_base_room_type == False)
        hotel_room_type_models = query.all()
        return [self._adaptor.to_domain_entity(model) for model in hotel_room_type_models]
