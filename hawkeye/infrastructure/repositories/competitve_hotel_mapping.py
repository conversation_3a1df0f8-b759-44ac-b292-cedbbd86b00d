import logging

from hawkeye.domain.entities.competitive_hotel_mapping import CompetitiveHotelMapping
from hawkeye.infrastructure.database.models import (
    CompetitiveHotelMapping as CompetitiveHotelMappingModel,
)
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.competitive_hotel_mapping_adaptor import (
    CompetitiveHotelMappingAdaptor,
)
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class CompetitiveHotelMappingRepository(BaseRepository):
    _model = CompetitiveHotelMappingModel
    _adaptor = CompetitiveHotelMappingAdaptor()

    def load_mapping_for_update(self, competitive_hotel_id, internal_hotel_id=None):
        query = self.query(self._model).filter(
            self._model.competitive_hotel_id == competitive_hotel_id,
        )

        if internal_hotel_id:
            query = query.filter(self._model.internal_hotel_id == internal_hotel_id)

        model = query.first()
        return self._adaptor.to_domain_entity(model) if model else None

    def load_all_mappings(self, hotel_id):
        competitive_hotel_models = self.query(self._model)
        competitive_hotel_models = competitive_hotel_models.filter(
            self._model.internal_hotel_id == hotel_id,
        )
        competitive_hotel_models = competitive_hotel_models.all()
        return [
            self._adaptor.to_domain_entity(competitive_hotel_model)
            for competitive_hotel_model in competitive_hotel_models
        ]

    def load_active_mappings(self, hotel_id):
        competitive_hotel_models = self.query(self._model)
        competitive_hotel_models = competitive_hotel_models.filter(
            self._model.internal_hotel_id == hotel_id,
            self._model.is_active == True,
        )
        competitive_hotel_models = competitive_hotel_models.all()
        return [
            self._adaptor.to_domain_entity(competitive_hotel_model)
            for competitive_hotel_model in competitive_hotel_models
        ]

    def create(self, competitive_hotel_mapping: CompetitiveHotelMapping):
        """Create a new CompetitiveHotelMapping record."""
        competitive_hotel_mapping_model = self._adaptor.to_db_entity(competitive_hotel_mapping)
        try:
            self._save(competitive_hotel_mapping_model)
        except Exception as ex:
            logger.error(f"Error creating CompetitiveHotelMapping: {ex}")
            raise DatabaseError(description=ex.__str__())

    def update(self, competitive_hotel_mapping: CompetitiveHotelMapping):
        """Update an existing CompetitiveHotelMapping record."""
        competitive_hotel_mapping_model = self._adaptor.to_db_entity(competitive_hotel_mapping)
        try:
            self._update(competitive_hotel_mapping_model)
        except Exception as ex:
            logger.error(f"Error updating CompetitiveHotelMapping: {ex}")
            raise DatabaseError(description=ex.__str__())

    def update_all(self, competitive_hotel_mappings: [CompetitiveHotelMapping]):
        competitive_hotel_mapping_models = [self._adaptor.to_db_entity(competitive_hotel_mapping)
                                            for competitive_hotel_mapping in competitive_hotel_mappings]
        try:
            self._update_all(competitive_hotel_mapping_models)
        except Exception as ex:
            logger.error(f"Error updating CompetitiveHotelMapping: {ex}")
            raise DatabaseError(description=ex.__str__())

    def create_all(self, competitive_hotel_mappings: [CompetitiveHotelMapping]):
        competitive_hotel_mapping_models = [
            self._adaptor.to_db_entity(competitive_hotel_mapping)
            for competitive_hotel_mapping in competitive_hotel_mappings
        ]
        try:
            super().create_all(competitive_hotel_mapping_models)
        except Exception as ex:
            logger.error(f"Error creating CompetitiveHotelMapping: {ex}")
            raise DatabaseError(description=ex.__str__())
