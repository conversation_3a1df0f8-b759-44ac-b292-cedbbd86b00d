from typing import List

from hawkeye.domain.entities.base_room_type_sku_price_rule import BaseRoomTypeSkuPriceRule
from hawkeye.infrastructure.database.models import BaseRoomTypeSkuPriceRuleModel
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.base_room_type_sku_price_rule_adaptor import (
    BaseRoomTypeSkuPriceRuleAdaptor
)
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class BaseRoomTypeSkuPriceRuleRepository(BaseRepository):
    _model = BaseRoomTypeSkuPriceRuleModel
    _adaptor = BaseRoomTypeSkuPriceRuleAdaptor()

    def create(self, base_room_type_sku_price_rule: BaseRoomTypeSkuPriceRule):
        base_room_type_sku_price_rule = self._adaptor.to_db_entity(base_room_type_sku_price_rule)
        self._save(base_room_type_sku_price_rule)

    def create_all(self, base_room_type_sku_price_rules: List[BaseRoomTypeSkuPriceRule]):
        base_room_type_sku_price_rules = [
            self._adaptor.to_db_entity(base_room_type_sku_price_rule)
            for base_room_type_sku_price_rule in base_room_type_sku_price_rules
        ]
        self._save_all(base_room_type_sku_price_rules)

    def load_all(self, hotel_ids=None):
        if hotel_ids:
            base_room_type_sku_price_rule_models = self.query(self._model).filter(self._model.hotel_id.in_(hotel_ids),
                                                                                  self._model.is_deleted == False)
        else:
            base_room_type_sku_price_rule_models = self.query(self._model).filter(self._model.is_deleted == False)

        base_room_type_sku_price_rule_models = base_room_type_sku_price_rule_models.order_by(
            self._model.created_at.desc(),
            self._model.priority.asc(),
        ).all()
        return [
            self._adaptor.to_domain_entity(base_room_type_sku_price_rule_model)
            for base_room_type_sku_price_rule_model in base_room_type_sku_price_rule_models
        ]

    def load_all_for_update(self, hotel_id, rule_type, sku_code):
        base_room_type_sku_price_rule_models = self.query(self._model).filter(self._model.is_deleted == False,
                                                                              self._model.hotel_id == hotel_id,
                                                                              self._model.sku_code == sku_code,
                                                                              self._model.rule_type ==
                                                                              rule_type).all()
        return [
            self._adaptor.to_domain_entity(base_room_type_sku_price_rule_model)
            for base_room_type_sku_price_rule_model in base_room_type_sku_price_rule_models
        ]

    def update_all(self, pricing_rules: List[BaseRoomTypeSkuPriceRule]):
        base_room_type_sku_price_rule_models = [
            self._adaptor.to_db_entity(pricing_rule) for pricing_rule in
            pricing_rules
        ]
        try:
            self._update_all(base_room_type_sku_price_rule_models)
        except Exception as ex:
            raise DatabaseError(description=str(ex)) from ex
