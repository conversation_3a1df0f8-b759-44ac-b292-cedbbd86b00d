from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus
from hawkeye.domain.entities.rule_file_upload import RuleFileUpload
from hawkeye.infrastructure.database.models import RuleFileUploadModel
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class RuleFileUploadAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: RuleFileUpload):
        return RuleFileUploadModel(
            id=domain_entity.file_id,
            file_name=domain_entity.file_name,
            path=domain_entity.path,
            uploaded_by=domain_entity.uploaded_by,
            uploaded_at=domain_entity.uploaded_at,
            status=domain_entity.status.value,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_model: RuleFileUploadModel) -> RuleFileUpload:
        return RuleFileUpload(
            file_id=db_model.id,
            file_name=db_model.file_name,
            path=db_model.path,
            uploaded_by=db_model.uploaded_by,
            uploaded_at=db_model.uploaded_at,
            status=RuleFileUploadStatus(db_model.status),
            modified_at=db_model.modified_at,
        )
