import logging
import os
from typing import Dict, Any

import redis
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.exceptions import InvalidTenantException
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from hawkeye.constants.hawkeye_constant import SERVICE_SHORT_NAME

logger = logging.getLogger(__name__)


def create_cache_configs(host, port, password, db):
    cache_configs = dict(host=host, port=port, db=db, password=password, charset="utf-8", decode_responses=True)
    return cache_configs


tenant_wise_caches = dict()


def get_redis_credentials():
    redis_configs = dict()
    active_tenants = TenantClient.get_active_tenants()

    for tenant in active_tenants:
        tenant_id = tenant.tenant_id
        redis_creds = AwsSecretManager.get_redis_credentials(tenant_id)
        redis_configs[tenant_id] = create_cache_configs(redis_creds['host'], redis_creds['port'],
                                                        redis_creds['password'], redis_creds['db'])

    return redis_configs


def setup_tenant_cache(tenant_id=None):
    """
    Setup a tenant wise StrictRedis objects. The setup should be done only once, on application startup. So this
    method is called on at module level.

    To access tenant cache, get the redis_cache for that specific tenant from the created tenant_wise_caches

    :return:
    """
    redis_configs = get_redis_credentials()
    if tenant_id:
        redis_config = redis_configs.get(tenant_id)
        if not redis_config:
            raise InvalidTenantException()

        tenant_wise_caches[tenant_id] = redis.StrictRedis(**redis_config)
    else:
        for tenant_id, redis_config in get_redis_credentials().items():
            if tenant_id in tenant_wise_caches:
                continue
            tenant_wise_caches[tenant_id] = redis.StrictRedis(**redis_config)


def get_cache(tenant_id=None):
    if not tenant_id:
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()

    cache = tenant_wise_caches.get(tenant_id)
    if not cache:
        setup_tenant_cache()
        cache = tenant_wise_caches.get(tenant_id)
        if not cache:
            raise InvalidTenantException()

    return cache


def mget_from_cache(keys_list):
    try:
        keys = []
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        for key in keys_list:
            keys.append(f'{tenant_id}.{SERVICE_SHORT_NAME}.{key}')

        return get_cache().mget(keys)
    except Exception as e:
        logger.info("Could not fetch data from cache. Exception: %s" % str(e))
        return [None for _ in keys_list]


def mset_in_cache(dictionary):
    if dictionary:
        try:
            tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
            for k, v in dictionary.items():
                key = f'{tenant_id}.{SERVICE_SHORT_NAME}.{k}'
                get_cache().set(
                    key, v, os.environ.get("NUM_HOURS_TO_EXPIRE_CACHE", 24) * 60 * 60
                )
            # self.cache_client.mset(dictionary)
            # commented because mset doesnt give option to set cache expiry
        except Exception as e:
            logger.info("Could not fetch data from cache. Exception: %s" % str(e))


def clear_cache_from_current_db():
    try:
        get_cache().flushdb()
    except Exception as e:
        logger.info("Could not flush Cache. Exception: %s" % str(e))
    return


def get_from_cache(key):
    try:
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        key = f'{tenant_id}.{SERVICE_SHORT_NAME}.{key}'
        return get_cache().get(key)
    except Exception as e:
        logger.info("Could not fetch data from cache. Exception: %s" % str(e))
        return


def set_in_cache(key, value, expiry=None):
    try:
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        key = f'{tenant_id}.{SERVICE_SHORT_NAME}.{key}'
        get_cache().set(key, value, ex=expiry)
        return
    except Exception as e:
        logger.info("Could not set data in cache. Exception: %s" % str(e))
        return


def get_ttl(key):
    return get_cache().pttl(key)


def mset_to_cache(key_value_mapping: Dict[str, Any], ttl: int = None) -> bool:
    """
    Set multiple key-value pairs to cache in a single operation

    Args:
        key_value_mapping: Dictionary of key-value pairs to set
        ttl: Time to live in seconds for all keys

    Returns:
        bool: True if successful
    """
    updated_key_value_mapping = {}
    tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
    for key, value in key_value_mapping.items():
        updated_key = f'{tenant_id}.{SERVICE_SHORT_NAME}.{key}'
        updated_key_value_mapping[updated_key] = value

    cache = get_cache()
    
    # Use pipeline for atomic operation
    pipe = cache.pipeline()
    
    try:
        # Set all key-value pairs
        pipe.mset(updated_key_value_mapping)
        
        # Set TTL for all keys if specified
        if ttl:
            for key in updated_key_value_mapping.keys():
                pipe.expire(key, ttl)
        
        # Execute all commands atomically
        pipe.execute()
        return True
        
    except redis.RedisError as e:
        logger.info(f"Error in bulk cache set: {str(e)}")
        return False
