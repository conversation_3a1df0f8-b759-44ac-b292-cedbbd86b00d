-- revision: '20240917184839_sku_price_prediction'
-- down_revision: '20240617180325_adding_created_at_and_modified_at_in_rom_type_price_rule'

-- upgrade

ALTER TABLE hotel_room_type ADD COLUMN max_adults INTEGER;
ALTER TABLE base_room_price RENAME COLUMN base_room_type TO room_type;
ALTER TABLE base_room_price ADD COLUMN sku_name VARCHAR;

ALTER TABLE pricing_rule RENAME TO base_room_type_price_rule;
ALTER TABLE base_room_price RENAME TO room_type_price;
ALTER TABLE room_type_price_rule RENAME TO incremental_price_rule;

ALTER INDEX ix_pricing_rule_hotel_created RENAME TO ix_room_type_price_rule_hotel_created;
ALTER INDEX ix_pricing_rule_created RENAME TO ix_room_type_price_rule_created;

DROP INDEX ix_price_trigger_id_hotel_date;
DROP INDEX ix_price_date_skipped_hotel;
CREATE INDEX idx_hrt_hotel_id_is_active ON hotel_room_type (is_active, hotel_id);


CREATE TABLE hotel_sku (
    id SERIAL PRIMARY KEY,
    hotel_id VARCHAR NOT NULL,
    sku_code VARCHAR NOT NULL,
    sku_category VARCHAR,
    rack_rate DECIMAL(15, 4),
    description TEXT,
    extra_information JSON,
    sku_name VARCHAR NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE INDEX idx_hst_hotel_id_sku_code ON hotel_sku (hotel_id, sku_code);
CREATE INDEX ix_hotel_sku ON hotel_sku (is_active, hotel_id);

ALTER TABLE hotel_sku ADD CONSTRAINT ix_hst_hotel_id_sku_code_uniq UNIQUE (hotel_id, sku_code);
ALTER TABLE hotel_sku ADD CONSTRAINT ix_hst_hotel_id_sku_name_uniq UNIQUE (hotel_id, sku_name);

CREATE TABLE base_room_type_sku_price_rule (
    rule_id SERIAL PRIMARY KEY,
    hotel_id VARCHAR NOT NULL,
    sku_code VARCHAR NOT NULL,
    input_type VARCHAR,
    rule_name VARCHAR,
    rule_type VARCHAR NOT NULL,
    config JSON,
    factor double precision,
    sum_factor double precision,
    priority INTEGER DEFAULT 1,
    start_price DECIMAL(15, 2),
    end_price DECIMAL(15, 2),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE INDEX ix_sku_pricing_rule_hotel_created ON base_room_type_sku_price_rule (hotel_id, created_at) WHERE NOT is_deleted;
CREATE INDEX ix_sku_pricing_rule_created ON base_room_type_sku_price_rule (created_at) WHERE NOT is_deleted;

-- downgrade
DROP TABLE IF EXISTS hotel_sku;
DROP TABLE IF EXISTS base_room_type_sku_price_rule;

ALTER TABLE base_room_type_price_rule RENAME TO pricing_rule;
ALTER TABLE hotel_room_type DROP COLUMN max_adults;
ALTER TABLE room_type_price RENAME TO base_room_price;
ALTER TABLE incremental_price_rule RENAME TO room_type_price_rule;

ALTER TABLE base_room_price RENAME COLUMN room_type TO base_room_type;
ALTER TABLE base_room_price DROP COLUMN sku_name;

ALTER INDEX ix_room_type_price_rule_hotel_created RENAME TO ix_pricing_rule_hotel_created;
ALTER INDEX ix_room_type_price_rule_created RENAME TO ix_pricing_rule_created;
CREATE INDEX ix_price_trigger_id_hotel_date ON base_room_price (price_trigger_id, hotel_id, target_date);
CREATE INDEX ix_price_date_skipped_hotel ON base_room_price (target_date, is_skipped, hotel_id);
DROP INDEX idx_hrt_hotel_id_is_active;
