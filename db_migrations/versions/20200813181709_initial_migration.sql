-- revision: '20200813181709_initial_migration'
-- down_revision: ''

-- upgrade

CREATE TABLE service_integration (
    service_code character varying NOT NULL,
    service_name character varying NOT NULL,
    sync_type character varying not NULL,
    is_active boolean not NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);


CREATE TABLE hotel (
    service_code character varying NOT NULL,
    internal_hotel_id character varying NOT NULL,
    external_hotel_id character varying NOT NULL,
    client_id character varying not NULL,
    client_secret character varying not NULL,
    is_amount_tax_inclusive boolean DEFAULT TRUE not NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted boolean DEFAULT FALSE not NULL
);


CREATE TABLE rate_plan_mapping (
    rate_plan_mapping_id integer NOT NULL,
    service_code character varying NOT NULL,
    internal_hotel_id character varying NOT NULL,
    internal_rate_plan_code character varying NOT NULL,
    external_rate_plan_code character varying NOT NULL,
    is_active boolean not NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);


CREATE TABLE room_type_mapping (
    room_type_mapping_id integer NOT NULL,
    service_code character varying NOT NULL,
    internal_hotel_id character varying NOT NULL,
    internal_room_type_code character varying NOT NULL,
    external_room_type_code character varying NOT NULL,
    is_active boolean not NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);


CREATE TABLE addon_mapping (
    addon_mapping_id integer NOT NULL,
    service_code character varying NOT NULL,
    internal_hotel_id character varying NOT NULL,
    internal_addon_code character varying NOT NULL,
    external_addon_code character varying NOT NULL,
    expense_item_id character varying NOT NULL,
    addon_name character varying NOT NULL,
    is_active boolean not NULL,
    pretax_price numeric(15,4),
    posttax_price numeric(15,4),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);


CREATE TABLE booking_sync (
    booking_sync_id integer NOT NULL,
    service_code character varying NOT NULL,
    internal_hotel_id character varying NOT NULL,
    internal_booking_id character varying,
    action_type character varying NOT NULL,
    external_booking_id character varying,
    last_booking_modification timestamp with time zone,
    data json,
    success boolean,
    acked boolean,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE TABLE inventory_sync (
    inventory_sync_id integer NOT NULL,
    service_code character varying NOT NULL,
    internal_hotel_id character varying NOT NULL,
    data json,
    success boolean,
    error character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);


CREATE TABLE client_call_log (
    id integer NOT NULL,
    client character varying NOT NULL,
    url character varying NOT NULL,
    headers json,
    call_type character varying NOT NULL,
    request_data character varying,
    response_data character varying,
    status_code integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE ONLY service_integration
    ADD CONSTRAINT service_integration_pkey PRIMARY KEY (service_code);


ALTER TABLE ONLY hotel ADD
    CONSTRAINT hotel_pkey PRIMARY KEY (service_code, internal_hotel_id);

ALTER TABLE ONLY hotel ADD
    CONSTRAINT unique_hotel_service_code_internal_hotel_id_external_hotel_id_key UNIQUE (service_code, internal_hotel_id, external_hotel_id);

ALTER TABLE ONLY hotel ADD
    CONSTRAINT unique_hotel_service_code_client_id_client_secret_key UNIQUE (service_code, client_id, client_secret);

ALTER TABLE ONLY hotel ADD
    CONSTRAINT hotel_service_code_fkey FOREIGN KEY (service_code) REFERENCES service_integration(service_code);


CREATE SEQUENCE addon_mapping_addon_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE addon_mapping_addon_mapping_id_seq OWNED BY addon_mapping.addon_mapping_id;

ALTER TABLE ONLY addon_mapping ALTER COLUMN addon_mapping_id SET DEFAULT nextval('addon_mapping_addon_mapping_id_seq'::regclass);

ALTER TABLE ONLY addon_mapping ADD
    CONSTRAINT addon_mapping_pkey PRIMARY KEY (addon_mapping_id);

ALTER TABLE ONLY addon_mapping ADD
    CONSTRAINT unique_addon_mapping_service_code_hotel_id_addon_active_key UNIQUE (service_code, internal_hotel_id, internal_addon_code, external_addon_code, is_active);

ALTER TABLE ONLY addon_mapping ADD
    CONSTRAINT addon_mapping_service_code_fkey FOREIGN KEY (service_code) REFERENCES service_integration(service_code);


CREATE SEQUENCE rate_plan_mapping_rate_plan_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE rate_plan_mapping_rate_plan_mapping_id_seq OWNED BY rate_plan_mapping.rate_plan_mapping_id;

ALTER TABLE ONLY rate_plan_mapping ALTER COLUMN rate_plan_mapping_id SET DEFAULT nextval('rate_plan_mapping_rate_plan_mapping_id_seq'::regclass);

ALTER TABLE ONLY rate_plan_mapping ADD
    CONSTRAINT rate_plan_mapping_pkey PRIMARY KEY (rate_plan_mapping_id);

ALTER TABLE ONLY rate_plan_mapping ADD
    CONSTRAINT rate_plan_mapping_service_code_hotel_id_rate_plan_active_key UNIQUE (service_code, internal_hotel_id, internal_rate_plan_code, external_rate_plan_code, is_active);

ALTER TABLE ONLY rate_plan_mapping ADD
    CONSTRAINT rate_plan_mapping_service_code_fkey FOREIGN KEY (service_code) REFERENCES service_integration(service_code);


CREATE SEQUENCE room_type_mapping_room_type_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE room_type_mapping_room_type_mapping_id_seq OWNED BY room_type_mapping.room_type_mapping_id;

ALTER TABLE ONLY room_type_mapping ALTER COLUMN room_type_mapping_id SET DEFAULT nextval('room_type_mapping_room_type_mapping_id_seq'::regclass);

ALTER TABLE ONLY room_type_mapping ADD
    CONSTRAINT room_type_mapping_pkey PRIMARY KEY (room_type_mapping_id);

ALTER TABLE ONLY room_type_mapping ADD
    CONSTRAINT room_type_mapping_service_code_hotel_id_room_type_active_key UNIQUE (service_code, internal_hotel_id, internal_room_type_code, external_room_type_code, is_active);

ALTER TABLE ONLY room_type_mapping ADD
    CONSTRAINT room_type_mapping_service_code_fkey FOREIGN KEY (service_code) REFERENCES service_integration(service_code);


CREATE SEQUENCE booking_sync_booking_sync_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE booking_sync_booking_sync_id_seq OWNED BY booking_sync.booking_sync_id;

ALTER TABLE ONLY booking_sync ALTER COLUMN booking_sync_id SET DEFAULT nextval('booking_sync_booking_sync_id_seq'::regclass);

ALTER TABLE ONLY booking_sync
    ADD CONSTRAINT booking_sync_pkey PRIMARY KEY (booking_sync_id);

ALTER TABLE ONLY booking_sync ADD
    CONSTRAINT booking_sync_service_code_fkey FOREIGN KEY (service_code) REFERENCES service_integration(service_code);


CREATE SEQUENCE inventory_sync_inventory_sync_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE inventory_sync_inventory_sync_id_seq OWNED BY inventory_sync.inventory_sync_id;

ALTER TABLE ONLY inventory_sync ALTER COLUMN inventory_sync_id SET DEFAULT nextval('inventory_sync_inventory_sync_id_seq'::regclass);

ALTER TABLE ONLY inventory_sync
    ADD CONSTRAINT inventory_sync_pkey PRIMARY KEY (inventory_sync_id);

ALTER TABLE ONLY inventory_sync ADD
    CONSTRAINT inventory_sync_service_code_fkey FOREIGN KEY (service_code) REFERENCES service_integration(service_code);


CREATE SEQUENCE client_call_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE client_call_log_id_seq OWNED BY client_call_log.id;

ALTER TABLE ONLY client_call_log ALTER COLUMN id SET DEFAULT nextval('client_call_log_id_seq'::regclass);

ALTER TABLE ONLY client_call_log
    ADD CONSTRAINT client_call_log_pkey PRIMARY KEY (id);
-- downgrade

DROP TABLE booking_sync;

DROP TABLE inventory_sync;

DROP TABLE addon_mapping;

DROP TABLE room_type_mapping;

DROP TABLE rate_plan_mapping;

DROP TABLE hotel;

DROP TABLE service_integration;

DROP TABLE client_call_log;

