import logging

from flask import current_app
from flask.cli import with_appcontext
from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient

from object_registry import inject
from tenant_gateway.application.services.tenant_gateway_service import TenantGatewayService
from tenant_gateway.common.constants import ChannelManager, ActionType
from tenant_gateway.common.decorators import worker_middleware
from tenant_gateway.common.exceptions import ChannelManagerNotEnabledException
from tenant_gateway.common.slack_alert_helper import SlackAlert
from tenant_gateway.domain.external_services.su.constants import BookingStatus
from tenant_gateway.infrastructure.consumers.base_consumer import BaseRMQConsumer
from tenant_gateway.infrastructure.consumers.consumer_config import SuBookingConfig
from tenant_gateway.infrastructure.external_clients.su_client.dtos.booking_dto import BookingDTO, CompanyDTO
from tenant_gateway.infrastructure.external_clients.cleartax_client import ClearTaxServiceClient
from tenant_gateway.infrastructure.telemetry.decorators import background_task

logger = logging.getLogger(__name__)


@inject(tenant_gateway_service=TenantGatewayService, cleartax_client=ClearTaxServiceClient)
class SuBookingConsumer(BaseRMQConsumer):
    def __init__(self, tenant_gateway_service, cleartax_client, tenant_id=TenantClient.get_default_tenant(),
                 routing_keys=None):
        self.su_booking_config = SuBookingConfig(tenant_id=tenant_id, routing_keys=routing_keys)
        self.service_code = ChannelManager.SU.value
        self.tenant_gateway_service = tenant_gateway_service
        self.cleartax_client = cleartax_client
        super().__init__(config=self.su_booking_config)
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)

    @serverless_function
    @worker_middleware
    @with_appcontext
    @background_task(name='su_booking_consumer')
    def process_message(self, body, message):
        consumer_event = body
        logger.info("Su process message called for message: %s", consumer_event.get('reservation_notif_id'))
        try:
            booking_dto = BookingDTO.convert_from_su_booking_payload(consumer_event)
            self.update_company_billing_address_using_gstin(booking_dto)
            if booking_dto.status == BookingStatus.NEW:
                self.tenant_gateway_service.create_booking(self.service_code, booking_dto)
            elif booking_dto.status == BookingStatus.MODIFIED:
                self.tenant_gateway_service.update_booking(self.service_code, booking_dto, ActionType.MODIFY)
            elif booking_dto.status == BookingStatus.CANCELLED:
                self.tenant_gateway_service.cancel_booking(self.service_code, booking_dto)
            logger.info('consumer_event %s', consumer_event)

        except ChannelManagerNotEnabledException:
            logger.error(f"channel manager is not enabled for {self.service_code}")
            return
        except Exception as exc:
            SlackAlert.send_alert(slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                                  text="Unable to process su booking event {event} with exception {exc}".
                                  format(exc=exc, event=consumer_event))
            logger.exception("Unable to process su booking event with exception {exc}".format(exc=exc))
            message.reject()
            return

        logger.info("Su Message Processed")
        message.ack()
        logger.info("Su Message Acknowledged")

    def update_company_billing_address_using_gstin(self, booking_dto):
        if not booking_dto.customer.corporate_details.gstin:
            return

        if not booking_dto.has_billing_company_details():
            return

        try:
            cleartax_data = self.cleartax_client.get_corporate_detail(booking_dto.customer.corporate_details.gstin)
            corporate_detail = CompanyDTO.create_from_cleartax_data(cleartax_data)
            booking_dto.update_billing_address(corporate_detail.address)
            if not booking_dto.has_billing_company():
                booking_dto.update_billing_company_name(corporate_detail.company_name)
            booking_dto.has_verified_corporate_gstin = True
        except Exception as e:
            # Note: we need to send communication in case of hotelcloud if gstin is not correct
            booking_dto.has_verified_corporate_gstin = False
            logger.info("Failed to update address using gstin {0}. Error {1}".format(
                booking_dto.customer.corporate_details.gstin, str(e)))

    def get_consumers(self, Consumer, channel):
        consumer = Consumer(queues=[self.queue], callbacks=[self.process_message])
        consumer.qos(prefetch_count=self.su_booking_config.prefetch_count, apply_global=True)
        return [consumer]
