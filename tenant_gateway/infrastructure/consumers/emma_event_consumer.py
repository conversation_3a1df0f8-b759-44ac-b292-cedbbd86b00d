import logging

from flask.cli import with_appcontext
from tenant_gateway.infrastructure.telemetry.decorators import background_task
import sentry_sdk
from sentry_sdk.integrations.serverless import serverless_function
from object_registry import inject
from tenant_gateway.common.constants import ServiceCodes
from tenant_gateway.common.decorators import worker_middleware
from tenant_gateway.domain.external_services.emma.constants import EMMA_PUBLISHER_ROUTING_KEY
from tenant_gateway.domain.external_services.emma.emma_service import EmmaService
from tenant_gateway.infrastructure.consumers.base_consumer import BaseRMQConsumer
from tenant_gateway.infrastructure.consumers.consumer_config import EmmaConfig
from tenant_gateway.infrastructure.external_clients.emma.emma_client import EmmaClient
logger = logging.getLogger(__name__)
@inject(emma_service=EmmaService, emma_client=EmmaClient)
class EmmaConsumer(BaseRMQConsumer):
    def __init__(self, tenant_id, primary, emma_service, emma_client):
        self.service_code = ServiceCodes.EMMA.value
        self.tenant_id = tenant_id
        self.emma_service = emma_service
        self.emma_client = emma_client
        super().__init__(EmmaConfig(self.tenant_id, routing_keys=[EMMA_PUBLISHER_ROUTING_KEY], primary=primary))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
    @serverless_function
    @worker_middleware
    @with_appcontext
    @background_task(name='emma_events_consumer')
    def process_message(self, body, message):
        xml_data = body
        logger.info(f"Emma process message called for payload: {xml_data}.")
        try:
            result = self.emma_service.process_event(self.tenant_id, xml_data)
            if result is not None:
                self.emma_client.send_result(result.request_id, result.to_xml())
            logger.info("Emma Message Processed")
            message.ack()
            logger.info("Emma Message Acknowledged")
        except Exception as exc:
            sentry_sdk.capture_exception(exc)
            logger.warning(f"Emma sending the result failed for {xml_data}.")
            # TODO: Move message into Dead Letter Queue instead.
            message.reject()
