import logging
from typing import List

from sqlalchemy import union

from object_registry import register_instance
from tenant_gateway.common.exceptions import DatabaseError
from tenant_gateway.domain.entities.hotel_room_inventory import HotelRoomInventory
from tenant_gateway.domain.models import HotelRoomInventoryModel
from tenant_gateway.infrastructure.database.adaptors.hotel_room_inventory_adaptor import (
    RoomTypeInventoryAdaptor,
)
from tenant_gateway.infrastructure.database.repositories.base_repository import (
    BaseRepository,
)

logger = logging.getLogger(__name__)


@register_instance()
class HotelRoomTypeInventoryRepository(BaseRepository):
    _model = HotelRoomInventoryModel
    _adaptor = RoomTypeInventoryAdaptor()

    def create(self, hotel_room_inventory: HotelRoomInventory):
        room_type_inventory_model = self._adaptor.to_db_entity(hotel_room_inventory)
        try:
            self._save(room_type_inventory_model)
        except Exception:
            raise DatabaseError

    def update(self, hotel_room_inventory: HotelRoomInventory):
        room_type_inventory_model = self._adaptor.to_db_entity(hotel_room_inventory)
        try:
            self._update(room_type_inventory_model)
        except Exception:
            raise DatabaseError

    def load_all(self):
        inventory_models = self.query(self._model).all()
        return [self._adaptor.to_domain_entity(model) for model in inventory_models]

    def load_room_type_inventories(self, hotel_id, room_type, start_date, end_date):
        inventory_models = (
            self.query(self._model)
            .filter(
                self._model.hotel_id == hotel_id,
                self._model.room_type == room_type,
                self._model.date >= start_date,
                self._model.date <= end_date,
            )
            .all()
        )
        return [self._adaptor.to_domain_entity(model) for model in inventory_models]

    def load_hotel_room_inventories(self, hotel_id, start_date, end_date):
        inventory_models = (
            self.query(self._model)
            .filter(
                self._model.hotel_id == hotel_id,
                self._model.date >= start_date,
                self._model.date <= end_date,
            )
            .all()
        )
        return [self._adaptor.to_domain_entity(model) for model in inventory_models]

    def load_all_inventories(self, inventories: List[HotelRoomInventory]) -> List[HotelRoomInventory]:
        queries = []
        for inventory in inventories:
            queries.append(
                self.query(self._model)
                    .filter(
                    self._model.hotel_id == inventory.hotel_id,
                    self._model.room_type == inventory.room_type,
                    self._model.date == inventory.date,
                )
            )
        query = self.query(self._model).from_statement(union(*queries))
        # union can't lock each query item for update
        # if locking is critical we need to use OR query instead of union
        # Going with union (without lock) bcz we only have single consumer sequentially doing inventory sync
        return [self._adaptor.to_domain_entity(row) for row in query.all()]

    def save_all(self, hotel_room_inventories: List[HotelRoomInventory]):
        room_type_inventory_models = [
            self._adaptor.to_db_entity(hotel_room_inventory)
            for hotel_room_inventory in hotel_room_inventories
        ]
        try:
            self._save_all(room_type_inventory_models)
        except Exception:
            raise DatabaseError

    def update_all(self, hotel_room_inventories: List[HotelRoomInventory]):
        room_type_inventory_models = [
            self._adaptor.to_db_entity(hotel_room_inventory)
            for hotel_room_inventory in hotel_room_inventories
        ]
        try:
            self._update_all(room_type_inventory_models)
        except Exception:
            raise DatabaseError
