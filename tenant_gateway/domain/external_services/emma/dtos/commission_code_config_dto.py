from typing import List

from pydantic import BaseModel

from tenant_gateway.common.exceptions import InvalidRequestError


class CommissionCodeValueDto(BaseModel):
    item_code: str
    amount: str
    amount_type: str
    description: str
    time_stamp: str


class CommissionCodeConfigDto(BaseModel):
    values: List[CommissionCodeValueDto]

    def __bool__(self):
        return bool(self.values)

    @staticmethod
    def create_dtos_from_config_value(config_value):
        commission_code_value_dtos = []
        for commission_config in config_value:
            commission_code_value_dtos.append(
                CommissionCodeValueDto(
                    item_code=commission_config["code"],
                    amount=commission_config["amount"],
                    amount_type=commission_config["amountType"],
                    description=commission_config["name"],
                    time_stamp=commission_config["time_stamp"],
                )
            )
        return CommissionCodeConfigDto(values=commission_code_value_dtos)

    def filter_applicable_commission_code(self, message_request):
        requested_commission_codes = []
        for request_item in message_request.request_items:
            if request_item[1].lower() == 'ALL'.lower():
                if not self.values:
                    raise InvalidRequestError(f"Commission codes have not been configured at Hotel Superhero.")
                return self
            else:
                commission_code_found = False
                for commission_code_value in self.values:
                    if commission_code_value.item_code == request_item[1]:
                        requested_commission_codes.append(commission_code_value)
                        commission_code_found = True
                if not commission_code_found:
                    raise InvalidRequestError(f"Commission code {request_item[1]} not found in config")
        self.values = requested_commission_codes
        return self

    def get_requested_commission_code(self, commission_code):
        return next(
            (
                commission_code_value for commission_code_value in self.values
                if commission_code_value.item_code == commission_code), None
        )
