from collections import namedtuple
from datetime import date
from decimal import Decimal

from tenant_gateway.domain.value_objects.occupancy import OccupancyVO


class Rate(object):

    UniqueKey = namedtuple(
        'UniqueKey',
        [
            'property_id',
            'rate_plan_id',
            'room_type_id',
            'occupancy',
            'stay_date',
        ]
    )

    def __init__(
        self,
        rate_plan_id: str,
        property_id: str,
        room_type_id: str,
        occupancy: OccupancyVO,
        price: Decimal,
        stay_date: date,
    ):
        self.rate_plan_id = rate_plan_id
        self.property_id = property_id
        self.room_type_id = room_type_id
        self.occupancy = occupancy
        self.price = price
        self.stay_date = stay_date
        self.unique_key = self.UniqueKey(
            property_id=self.property_id,
            rate_plan_id=self.rate_plan_id,
            room_type_id=self.room_type_id,
            occupancy=self.occupancy,
            stay_date=self.stay_date,
        )

    @property
    def adult_count(self):
        return self.occupancy.adult_count

    def update_price(self, price):
        self.price = price
