from psycogreen.gevent import patch_psycopg
import os


bind = '0.0.0.0:8000'

reload = True
workers = 5 if os.environ.get('APP_ENV') in ('production', 'prod') else 1
worker_class = 'gevent'
worker_connections = 10
timeout = 7200

accesslog = os.environ.get('LOG_ROOT') + 'po_gunicorn.log'
errorlog = os.environ.get('LOG_ROOT') + 'po_gunicorn_error.log'

loglevel = 'debug'

proc_name = 'pricing_orchestrator'

def post_fork(server, worker):
    # patch psycopg2 for gevent compatibility
    patch_psycopg()
