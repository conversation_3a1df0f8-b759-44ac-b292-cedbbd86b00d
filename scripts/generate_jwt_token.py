import os
import jwt


def generate_token(client_id):
    """
    Generate a JWT token with the given secret key and expiry.
    
    Args:
        client_id: The secret key to sign the token
        
    Returns:
        str: The generated JWT token
    """
    payload = {"client_id": client_id}
    secret_key = os.environ.get('SECRET_KEY')
    token = jwt.encode(payload, secret_key, algorithm='HS256')
    return token


if __name__ == "__main__":
    client_id = "goa-police"
    print(generate_token(client_id))
