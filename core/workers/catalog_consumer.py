#!/usr/bin/env python
# -*- coding: utf-8 -*-
import logging
import os
import sys
from argparse import ArgumentParser

import newrelic.agent
from treebo_commons.multitenancy.tenant_client import TenantClient

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from core.common.rabbitmq.exceptions import Reject
from rackrate_service.application.catalog_sync_service import CatalogSyncService
from core.seller.services.seller_crud import seller_crud_service
from rackrate_service.infrastructure.consumers.serializers.cs_catalog_schema import (
    CatalogLPropertyPayloadSchema,
    CatalogChannelPayloadSchema,
    CatalogLSellerPayloadSchema, CatalogSKUPayloadSchema, CatalogSellerSkuPayloadSchema)
from core.common.rabbitmq.utils import maybe_jsonify
from rackrate_service.extensions import rmq
from rackrate_service.decorators import consumer_middleware
from core.common.globals import global_context

logger = logging.getLogger(__name__)

PROPERTY_RK = 'com.cs.property'
SELLER_RK = 'com.cs.seller'
CHANNEL_RK = 'com.cs.treebo.channel'
SKU_RK = 'com.cs.sku'
SELLER_SKU_RK = 'com.cs.sellersku'

cs_exchange = rmq.E('cs-events', 'topic')

catalog_queue = rmq.Q(
    queue_name='rackrate-catalog-events',
    routing_key='com.cs.#',
    exchange=cs_exchange,
)


@newrelic.agent.background_task(name='start_catalog_consumer')
@consumer_middleware
@maybe_jsonify
def process_message(body, message):
    cataloging_sync_service = CatalogSyncService()

    routing_key = message.delivery_info['routing_key']
    if routing_key == PROPERTY_RK:
        validator, method = CatalogLPropertyPayloadSchema, cataloging_sync_service.sync_property
    elif routing_key == SELLER_RK:
        validator, method = CatalogLSellerPayloadSchema, seller_crud_service.sync_seller
    elif routing_key == SELLER_SKU_RK:
        validator, method = CatalogSellerSkuPayloadSchema, seller_crud_service.sync_seller_sku
    elif routing_key == CHANNEL_RK:
        validator, method = CatalogChannelPayloadSchema, cataloging_sync_service.sync_channel_data
    elif routing_key == SKU_RK:
        validator, method = CatalogSKUPayloadSchema, cataloging_sync_service.sync_sku
    else:
        return

    data, errors = validator().load(body)
    if errors:
        message = f"Invalid payload for: {message.routing_key}. Failed due to {errors}"
        raise Reject(message)
    method(data)


def main(tenant_id):
    global_context.set_tenant_id(tenant_id=tenant_id)
    from autoapp import app
    with app.app_context():
        rmq.consume(
            callback=process_message,
            queue=catalog_queue,
            enable_retries=True
        )


if __name__ == '__main__':
    parser = ArgumentParser()

    parser.add_argument('--tenant_id',
                        help="Tenant id of the tenant",
                        default=TenantClient.get_default_tenant())
    args = parser.parse_args()
    sys.exit(main(tenant_id=args.tenant_id) or 0)
