from abc import ABC, abstractmethod

from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request


class BaseRepo(ABC):

    def __init__(self, price_aggregator):
        self.price_aggregator = price_aggregator

    def fetch(self):
        request = self._prepare_request()
        data = self._fetch(request)
        self._parse_response(data)

    @abstractmethod
    def _prepare_request(self):
        pass

    @abstractmethod
    def _parse_response(self, data):
        pass

    @staticmethod
    def get_headers():
        headers = {
            "referrer": "/",
            "content-type": "application/json"
        }
        enrich_outgoing_request(headers)
        return headers
