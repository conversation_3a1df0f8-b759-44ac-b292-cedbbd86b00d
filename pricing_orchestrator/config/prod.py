from pricing_orchestrator.config.base import Base
import os


class ProdConfig(Base):
    DEBUG = False
    RR_DOMAIN = os.environ.get("RR_DOMAIN", "http://rackrate.treebo.be")
    DISCOUNT_DOMAIN = os.environ.get("DISCOUNT_DOMAIN", "http://koopan.treebo.be")
    TAX_DOMAIN = os.environ.get("TAX_DOMAIN", "http://tax-staging.treebo.be")
    CATALOG_DOMAIN = os.environ.get("CATALOG_DOMAIN", "https://catalog.treebo.com")
    SLACK_URL = os.environ.get("SLACK_URL",
                               "*****************************************************************************")
    AWS_ACCESS_KEY_ID = os.environ.get("AWS_ACCESS_KEY_ID", "********************")
    AWS_SECRET_ACCESS_KEY = os.environ.get("AWS_SECRET_ACCESS_KEY", "NHJzG+36IIOQ5gSzFHNcbVtJuVHGVwx50fJop1Qm")
    ARN = os.environ.get("ARN", "arn:aws:lambda:ap-south-1:605536185498:function:Direct-Price-Push-DP")

