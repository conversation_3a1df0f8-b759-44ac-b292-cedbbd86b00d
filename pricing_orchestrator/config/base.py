import json
import os
import flask
from treebo_commons.request_tracing.flask import before_request, after_request


class Base(object):
    SECRET_KEY = 'secret-key'
    APP_DIR = os.path.abspath(os.path.dirname(__file__))  # This directory
    PROJECT_ROOT = os.path.abspath(os.path.join(APP_DIR, os.pardir))
    BCRYPT_LOG_ROUNDS = 13
    LOG_ROOT = os.environ.get('LOG_ROOT', "/var/logs/")
    DEBUG_TB_ENABLED = False  # Disable Debug toolbar
    DEBUG_TB_INTERCEPT_REDIRECTS = False
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    WEBPACK_MANIFEST_PATH = 'webpack/manifest.json'
    ENV = os.environ.get("ENV", "local")

    # Middlewares
    WSGI_MIDDLEWARES = []
    BEFORE_REQUEST_MIDDLEWARES = [lambda: before_request(flask.request)]
    AFTER_REQUEST_MIDDLEWARES = [lambda resp: after_request(resp, flask.request)]


class LogstashJsonEncoder(json.JSONEncoder):
    def __init__(self, skipkeys=False, ensure_ascii=True,
                 check_circular=True, allow_nan=True, sort_keys=False,
                 indent=None, separators=None, encoding='utf-8', default=None):
        super(LogstashJsonEncoder, self).__init__(skipkeys=True, ensure_ascii=ensure_ascii,
                                                  check_circular=check_circular,
                                                  allow_nan=allow_nan, sort_keys=sort_keys,
                                                  indent=indent, separators=separators,
                                                  encoding=encoding, default=default)
