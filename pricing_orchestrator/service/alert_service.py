import os

from pricing_orchestrator.constants import SHARED_INFRA_ALERT_SLACK_URL, API_RESPONSE_LIMIT, PROD_ENV
from pricing_orchestrator.slack_alert import SlackClient


class AlertService:

    @staticmethod
    def send_api_delay_alert(end_time, start_time, api_url, request_data):
        # Note: end_time and start_time should be in msec
        if not (end_time - start_time) >= API_RESPONSE_LIMIT or os.environ.get("ENV") != PROD_ENV:
            return
        SlackClient.alert(
            "Alert!.Time taken for url: {} is {} msec. Request data:{}".format(api_url, (end_time-start_time), request_data),
            SHARED_INFRA_ALERT_SLACK_URL)
