import json
import logging
from collections import defaultdict

import boto3
from flask import current_app

from pricing_orchestrator.adaptors.aggregator_request_adaptor import AggregatorAdaptor
from pricing_orchestrator.adaptors.aggregator_request_v2_adaptor import AggregatorAdaptorV2
from pricing_orchestrator.domains.aggregator.price_aggregator import PriceAggregator
from pricing_orchestrator.slack_alert import <PERSON><PERSON>ck<PERSON><PERSON>
from pricing_orchestrator.utils import get_epochtime_ms

logger = logging.getLogger(__name__)


class PriceService:
    def __init__(self):
        aws_access_key_id = current_app.config.get("AWS_ACCESS_KEY_ID")
        aws_secret_access_key = current_app.config.get("AWS_SECRET_ACCESS_KEY")
        self.arn = current_app.config.get("ARN")
        self.client = boto3.client('firehose', aws_access_key_id=aws_access_key_id,
                                   aws_secret_access_key=aws_secret_access_key,
                                   region_name='ap-south-1')

    def get_price(self, price_request, debug=False):
        if not price_request:
            return None
        price_aggregator = PriceAggregator()
        aggregator_adaptor = AggregatorAdaptor(price_aggregator)
        aggregator_adaptor.prepare_request(price_request.data)
        price_aggregator.get_price()
        price_aggregator.get_discounts()
        price_aggregator.get_tax()
        start = get_epochtime_ms()

        if debug:
            pricing_strategy_steps = defaultdict()
            global rackrate

            # calculating pricing_strategy_steps for rackrate
            for hotel_price in price_aggregator.rack_rate['hotel_prices']:
                pricing_strategy_steps['hotel_id'] = hotel_price['property_code']
                for sku_price in hotel_price['sku_prices']:
                    pricing_strategy_steps['sku_code'] = sku_price['sku_details']['code']
                    rackrate = {
                        'guardrails_max_price': min(sku_price['guardrails']['global_max_price'],
                                                    sku_price['guardrails']['max_price']),
                        'guardrails_min_price': max(sku_price['guardrails']['global_min_price'],
                                                    sku_price['guardrails']['min_price']),
                    }
                    for policy_price in sku_price['policy_prices']:
                        pricing_strategy_steps['policy'] = policy_price['policy']
                        pricing_strategy_steps['mode'] = price_aggregator.request.filters.mode
                        pricing_strategy_steps['price_per_date'] = {}
                        for price_per_date in policy_price['price_per_date']:
                            pricing_strategy_steps['price_per_date'][price_per_date['date']] = {
                                'rackrate': {
                                    'list_price': price_per_date['prices']['list_price'],
                                    'sale_price': price_per_date['prices']['sale_price']
                                },
                                'discount': {},
                                'taxes': {}
                            }
            pricing_strategy_steps['rackrate'] = rackrate

            # calculating pricing_strategy_steps for discount
            discount_code = ""
            for hotel in price_aggregator.discounted_price['hotels']:
                for applied_discounts in hotel['applied_discounts']:
                    if applied_discounts['code']:
                        discount_code = applied_discounts['code']
                for sku in hotel['skus']:
                    for dates in sku['dates']:
                        pricing_strategy_steps['price_per_date'][dates['date']]['discount'] = {
                            'sell_price': dates['sell_price'],
                            'implicit_discount_value': dates['implicit_discount_value'],
                            'explicit_discount_value': dates['explicit_discount_value'],
                            'explicit_discount_code': discount_code,
                            'flat_discount_value': dates['flat_discount_value'],
                            'discount_value': dates['discount_value'],
                            'discounted_sell_price': dates['discounted_sell_price']
                        }

            # calculating pricing_strategy_steps for tax
            for sku in price_aggregator.taxes['skus']:
                pricing_strategy_steps['taxes'] = {
                    'taxes_per_date': []
                }
                for taxes_per_date in sku['prices']:
                    pricing_strategy_steps['price_per_date'][taxes_per_date['date']]['taxes'] = {
                        'date': taxes_per_date['date'],
                        'pre_tax_price': taxes_per_date['pre_tax_price'],
                        'total_tax': taxes_per_date['tax'][0]['value'],
                        'total_percentage': taxes_per_date['tax'][0]['percent'],
                        'post_tax_price': taxes_per_date['post_tax_price']
                    }
            return pricing_strategy_steps
        else:
            price_aggregator.prepare_response()

        logger.info("Time taken prepare response %s", get_epochtime_ms() - start)
        result = json.loads(json.dumps(price_aggregator.response, default=lambda obj: obj.__dict__))
        try:
            start = get_epochtime_ms()
            # self.dp_push(result)
            end = get_epochtime_ms()
            logger.info("time taken in DP push: %s", end - start)
        except Exception as ex:
            error_message = "Exception: %s while doing DP push" % ex
            logger.exception(error_message)
            SlackClient.alert(error_message)
        return result

    @staticmethod
    def get_price_v2(price_request):
        if not price_request:
            return None
        price_aggregator = PriceAggregator()
        aggregator_adaptor = AggregatorAdaptorV2(price_aggregator)
        aggregator_adaptor.prepare_request(price_request.data)
        price_aggregator.get_price()
        price_aggregator.get_discounts()
        price_aggregator.get_tax()
        price_aggregator.prepare_response_v2()

        result = json.loads(json.dumps(price_aggregator.response, default=lambda obj: obj.__dict__))
        return result

    def dp_push(self, data):
        try:
            env = current_app.config.get("ENV")
            if env != 'prod':
                logger.info("Ignoring the DP push for %s environment", env)
                return
            self.client.put_record(DeliveryStreamName='Direct-Price-Push-DP', Record={'Data': json.dumps(data) + '\n'})
            logger.info("Response successfully pushed to DP")
        except Exception as ex:
            error_message = "Exception: %s while pushing po search data to DP" % ex
            logger.exception(error_message)
            SlackClient.alert(error_message)
